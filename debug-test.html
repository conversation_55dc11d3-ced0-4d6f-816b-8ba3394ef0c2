<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص المشكلة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>تشخيص مشكلة الأزرار</h1>
    
    <button class="btn" onclick="testBasic()">اختبار أساسي</button>
    <button class="btn" onclick="testConsole()">فحص وحدة التحكم</button>
    
    <div id="results"></div>

    <script>
        // تسجيل جميع الأخطاء
        window.addEventListener('error', function(e) {
            console.error('خطأ JavaScript:', e.error);
            addResult('❌ خطأ JavaScript: ' + e.error.message, 'error');
        });

        window.addEventListener('unhandledrejection', function(e) {
            console.error('Promise مرفوض:', e.reason);
            addResult('❌ Promise مرفوض: ' + e.reason, 'error');
        });

        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function testBasic() {
            addResult('✅ الزر الأساسي يعمل!', 'success');
        }

        function testConsole() {
            addResult('🔍 فحص وحدة التحكم...', 'info');
            
            // فحص الأخطاء في وحدة التحكم
            const errors = [];
            
            // محاولة تحميل كل ملف
            const scripts = [
                'js/xlsx.min.js',
                'js/utils.js', 
                'js/excel-electron.js',
                'js/database.js',
                'js/students.js',
                'js/grades.js',
                'js/final-results.js',
                'js/reports.js',
                'js/app.js'
            ];
            
            scripts.forEach(script => {
                const scriptElement = document.createElement('script');
                scriptElement.src = script;
                scriptElement.onload = () => {
                    addResult(`✅ تم تحميل ${script}`, 'success');
                };
                scriptElement.onerror = () => {
                    addResult(`❌ فشل تحميل ${script}`, 'error');
                };
                document.head.appendChild(scriptElement);
            });
        }

        // فحص تلقائي عند التحميل
        window.onload = function() {
            addResult('🚀 بدء التشخيص...', 'info');
            
            setTimeout(() => {
                // فحص المتغيرات العامة
                const globals = [
                    'XLSX',
                    'showNotification',
                    'showSection', 
                    'closeModal',
                    'importExcel',
                    'studentsManager',
                    'electronExcelManager'
                ];
                
                globals.forEach(global => {
                    if (typeof window[global] !== 'undefined') {
                        addResult(`✅ ${global} متاح`, 'success');
                    } else {
                        addResult(`❌ ${global} غير متاح`, 'error');
                    }
                });
            }, 2000);
        };
    </script>
</body>
</html>
