@echo off
title IT Evaluation System Build

echo.
echo ========================================
echo    Building IT Evaluation System
echo ========================================
echo.

REM Check for Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Node.js is not installed
    echo.
    echo Please install Node.js from: https://nodejs.org
    echo.
    pause
    exit /b 1
)

echo SUCCESS: Node.js found
echo.

REM Check for package.json
if not exist "package.json" (
    echo ERROR: package.json not found
    echo Make sure you are in the correct project directory
    pause
    exit /b 1
)

echo Installing dependencies...
call npm install
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo SUCCESS: Dependencies installed
echo.

echo Cleaning previous builds...
if exist "dist" rmdir /s /q "dist"
if exist "build\output" rmdir /s /q "build\output"

echo.
echo Choose build type:
echo 1. Windows (64-bit)
echo 2. Windows (32-bit)
echo 3. Windows (both)
echo 4. Portable
echo 5. All types
echo.

set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" (
    echo Building for Windows 64-bit...
    call npx electron-builder --win --x64
    goto :build_done
) else if "%choice%"=="2" (
    echo Building for Windows 32-bit...
    call npx electron-builder --win --ia32
    goto :build_done
) else if "%choice%"=="3" (
    echo Building for Windows (both)...
    call npx electron-builder --win --x64 --ia32
    goto :build_done
) else if "%choice%"=="4" (
    echo Building portable version...
    call npx electron-builder --win --portable
    goto :build_done
) else if "%choice%"=="5" (
    echo Building all types...
    call npx electron-builder --win --x64 --ia32
    goto :build_done
) else (
    echo ERROR: Invalid choice
    echo Please run the script again and choose a number between 1-5
    pause
    exit /b 1
)

:build_done

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ERROR: Build failed
    echo Check the errors above
    echo.
    echo Press any key to exit...
    pause
    exit /b 1
)

echo.
echo SUCCESS: Build completed!
echo.

REM Show built files
if exist "dist" (
    echo Built files in dist folder:
    echo.
    dir /b "dist\*.exe" 2>nul
    dir /b "dist\*.msi" 2>nul
    dir /b "dist\*.zip" 2>nul
    echo.

    echo Open dist folder? (y/n)
    set /p open="Your choice: "
    if /i "%open%"=="y" (
        start "" "dist"
    )
)

echo.
echo Build completed successfully!
echo.
echo Notes:
echo - .exe file is the main installer
echo - .zip file contains portable version
echo - You can distribute any of these files
echo.
echo Press any key to exit...
pause
goto :eof
