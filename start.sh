#!/bin/bash

# تعيين الترميز
export LANG=ar_SA.UTF-8

echo ""
echo "========================================"
echo "    استمارة تقويم تقنية المعلومات"
echo "========================================"
echo ""

# التحقق من وجود Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js غير مثبت على النظام"
    echo ""
    echo "يرجى تثبيت Node.js من الرابط التالي:"
    echo "https://nodejs.org"
    echo ""
    echo "أو يمكنك فتح ملف index.html مباشرة في المتصفح"
    echo ""
    read -p "اضغط Enter للمتابعة..."
    exit 1
fi

echo "✅ تم العثور على Node.js"
echo ""

# التحقق من وجود package.json
if [ ! -f "package.json" ]; then
    echo "❌ ملف package.json غير موجود"
    echo "تأكد من أنك في المجلد الصحيح للمشروع"
    read -p "اضغط Enter للمتابعة..."
    exit 1
fi

# التحقق من وجود node_modules
if [ ! -d "node_modules" ]; then
    echo "📦 تثبيت التبعيات..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ فشل في تثبيت التبعيات"
        read -p "اضغط Enter للمتابعة..."
        exit 1
    fi
    echo "✅ تم تثبيت التبعيات بنجاح"
    echo ""
fi

echo "🚀 بدء تشغيل التطبيق..."
echo ""
echo "سيتم فتح التطبيق في نافذة منفصلة..."
echo "لإغلاق التطبيق، أغلق هذه النافذة أو اضغط Ctrl+C"
echo ""

# تشغيل التطبيق
npm start

echo ""
echo "تم إغلاق التطبيق"
read -p "اضغط Enter للمتابعة..."
