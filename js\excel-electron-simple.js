// نسخة مبسطة من Excel Manager لـ Electron
// هذا الملف يوفر دعم أساسي لوظائف Excel

// فحص بيئة Electron بشكل آمن
function isElectronEnvironment() {
    try {
        if (typeof window === 'undefined') return false;
        if (typeof window.process === 'undefined') return false;
        if (!window.process) return false;
        if (typeof window.process.type === 'undefined') return false;
        return window.process.type === 'renderer';
    } catch (error) {
        return false;
    }
}

// مدير Excel مبسط
const simpleExcelManager = {
    isElectron: isElectronEnvironment(),
    
    init: function() {
        console.log('Excel Manager مبسط - البيئة:', this.isElectron ? 'Electron' : 'متصفح');
    },
    
    async saveExcelFile(workbook, fileName) {
        if (!workbook) {
            throw new Error('لا يوجد ملف Excel للحفظ');
        }

        try {
            if (this.isElectron && typeof window.electronAPI !== 'undefined') {
                // استخدام Electron API
                const buffer = XLSX.write(workbook, { 
                    type: 'buffer', 
                    bookType: 'xlsx' 
                });
                
                const result = await window.electronAPI.excelSaveFile(
                    buffer, 
                    fileName || 'ملف_Excel.xlsx'
                );
                
                if (result.success) {
                    return { success: true, filePath: result.filePath };
                } else if (result.canceled) {
                    return { success: false, canceled: true };
                } else {
                    throw new Error(result.error || 'فشل في حفظ الملف');
                }
            } else {
                // استخدام الطريقة العادية للمتصفح
                XLSX.writeFile(workbook, fileName || 'ملف_Excel.xlsx');
                return { success: true, method: 'browser' };
            }
        } catch (error) {
            console.error('خطأ في حفظ ملف Excel:', error);
            throw error;
        }
    },

    async readExcelFile(filePath) {
        try {
            if (this.isElectron && typeof window.electronAPI !== 'undefined') {
                // استخدام Electron API
                const result = await window.electronAPI.excelReadFile(filePath);
                
                if (result.success) {
                    const workbook = XLSX.read(result.data, { type: 'buffer' });
                    return { 
                        success: true, 
                        workbook: workbook,
                        filePath: result.filePath 
                    };
                } else if (result.canceled) {
                    return { success: false, canceled: true };
                } else {
                    throw new Error(result.error || 'فشل في قراءة الملف');
                }
            } else {
                // في المتصفح العادي، نحتاج إلى input file
                return new Promise((resolve, reject) => {
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = '.xlsx,.xls';
                    
                    input.onchange = (e) => {
                        const file = e.target.files[0];
                        if (!file) {
                            resolve({ success: false, canceled: true });
                            return;
                        }

                        const reader = new FileReader();
                        reader.onload = (event) => {
                            try {
                                const data = new Uint8Array(event.target.result);
                                const workbook = XLSX.read(data, { type: 'array' });
                                resolve({ 
                                    success: true, 
                                    workbook: workbook,
                                    fileName: file.name 
                                });
                            } catch (error) {
                                reject(error);
                            }
                        };
                        reader.onerror = () => reject(new Error('فشل في قراءة الملف'));
                        reader.readAsArrayBuffer(file);
                    };
                    
                    input.click();
                });
            }
        } catch (error) {
            console.error('خطأ في قراءة ملف Excel:', error);
            throw error;
        }
    },

    extractDataFromWorkbook(workbook, sheetIndex = 0) {
        if (!workbook || !workbook.SheetNames || workbook.SheetNames.length === 0) {
            throw new Error('ملف Excel فارغ أو تالف');
        }

        const sheetName = workbook.SheetNames[sheetIndex];
        const worksheet = workbook.Sheets[sheetName];
        
        return XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    }
};

// تهيئة المدير
try {
    simpleExcelManager.init();
} catch (error) {
    console.error('خطأ في تهيئة Excel Manager:', error);
}

// جعل المدير متاحاً عالمياً
if (typeof window !== 'undefined') {
    window.electronExcelManager = simpleExcelManager;
}

console.log('تم تحميل Excel Manager المبسط بنجاح');
