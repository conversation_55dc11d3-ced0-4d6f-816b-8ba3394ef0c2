<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإصلاحات السريعة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn-success {
            background: #27ae60;
        }
        .btn-success:hover {
            background: #229954;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>اختبار الإصلاحات السريعة</h1>
    
    <div class="result">
        <h3>اختبار الدوال الأساسية:</h3>
        <button class="btn" onclick="testBasicFunctions()">اختبار الدوال الأساسية</button>
        <button class="btn" onclick="testManagers()">اختبار المدراء</button>
        <button class="btn" onclick="testStudentFunctions()">اختبار دوال الطلاب</button>
    </div>

    <div class="result">
        <h3>اختبار الأزرار الفعلية:</h3>
        <button class="btn btn-success" onclick="showAddStudentModal()">إضافة طالب</button>
        <button class="btn btn-success" onclick="showImportModal()">استيراد Excel</button>
        <button class="btn btn-success" onclick="exportStudentsToExcel()">تصدير Excel</button>
    </div>
    
    <div id="test-results" class="result">
        انقر على أي زر لبدء الاختبار...
    </div>

    <!-- تحميل الملفات بالترتيب الصحيح -->
    <script src="js/xlsx.min.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/excel-electron-simple.js"></script>
    <script src="js/database.js"></script>
    <script src="js/students.js"></script>
    <script src="js/grades.js"></script>
    <script src="js/final-results.js"></script>
    <script src="js/reports.js"></script>
    <script src="js/app.js"></script>

    <script>
        function updateResults(message, type = 'info') {
            const results = document.getElementById('test-results');
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : '';
            results.innerHTML = `<div class="${className}">${message}</div>`;
        }

        function testBasicFunctions() {
            let results = [];
            
            const functions = [
                'showNotification',
                'showSection',
                'closeModal',
                'showModal'
            ];
            
            functions.forEach(func => {
                if (typeof window[func] === 'function') {
                    results.push(`✅ ${func}`);
                } else {
                    results.push(`❌ ${func}`);
                }
            });
            
            updateResults(results.join('<br>'), results.some(r => r.includes('❌')) ? 'error' : 'success');
        }

        function testManagers() {
            let results = [];
            
            const managers = [
                'dbManager',
                'studentsManager',
                'gradesManager',
                'finalResultsManager',
                'reportsManager'
            ];
            
            managers.forEach(manager => {
                if (typeof window[manager] !== 'undefined' && window[manager]) {
                    results.push(`✅ ${manager}`);
                } else {
                    results.push(`❌ ${manager}`);
                }
            });
            
            updateResults(results.join('<br>'), results.some(r => r.includes('❌')) ? 'error' : 'success');
        }

        function testStudentFunctions() {
            let results = [];
            
            const functions = [
                'showAddStudentModal',
                'showImportModal',
                'exportStudentsToExcel'
            ];
            
            functions.forEach(func => {
                if (typeof window[func] === 'function') {
                    results.push(`✅ ${func}`);
                } else {
                    results.push(`❌ ${func}`);
                }
            });
            
            updateResults(results.join('<br>'), results.some(r => r.includes('❌')) ? 'error' : 'success');
        }

        // تسجيل الأخطاء
        window.addEventListener('error', function(e) {
            console.error('خطأ JavaScript:', e.error);
            updateResults(`❌ خطأ JavaScript: ${e.error.message} في ${e.filename}:${e.lineno}`, 'error');
        });

        window.addEventListener('unhandledrejection', function(e) {
            console.error('Promise مرفوض:', e.reason);
            updateResults(`❌ Promise مرفوض: ${e.reason}`, 'error');
        });

        // اختبار تلقائي عند التحميل
        window.onload = function() {
            setTimeout(() => {
                updateResults('🚀 تم تحميل الصفحة. جاهز للاختبار!', 'success');
                
                // اختبار تلقائي للمدراء
                setTimeout(() => {
                    testManagers();
                }, 1000);
            }, 500);
        };
    </script>
</body>
</html>
