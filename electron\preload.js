// ملف Preload لـ Electron
// يوفر واجهة آمنة بين العملية الرئيسية وعملية العرض

const { contextBridge, ipcRenderer } = require('electron');

// تعريض APIs آمنة لعملية العرض
contextBridge.exposeInMainWorld('electronAPI', {
    // وظائف النافذة
    minimizeWindow: () => ipcRenderer.invoke('minimize-window'),
    maximizeWindow: () => ipcRenderer.invoke('maximize-window'),
    closeWindow: () => ipcRenderer.invoke('close-window'),
    
    // وظائف الملفات
    showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
    showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
    
    // وظائف النظام
    showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
    openExternal: (url) => ipcRenderer.invoke('open-external', url),
    
    // وظائف التطبيق
    getAppVersion: () => ipcRenderer.invoke('get-app-version'),
    getAppPath: () => ipcRenderer.invoke('get-app-path'),
    
    // أحداث النافذة
    onWindowMaximized: (callback) => ipcRenderer.on('window-maximized', callback),
    onWindowUnmaximized: (callback) => ipcRenderer.on('window-unmaximized', callback),
    
    // وظائف الطباعة
    print: () => ipcRenderer.invoke('print'),
    printToPDF: (options) => ipcRenderer.invoke('print-to-pdf', options),
    
    // وظائف قاعدة البيانات
    exportDatabase: () => ipcRenderer.invoke('export-database'),
    importDatabase: (filePath) => ipcRenderer.invoke('import-database', filePath),
    
    // وظائف التحديث
    checkForUpdates: () => ipcRenderer.invoke('check-for-updates'),
    installUpdate: () => ipcRenderer.invoke('install-update'),

    // وظائف Excel المحسنة
    excelSaveFile: (data, fileName) => ipcRenderer.invoke('excel-save-file', data, fileName),
    excelReadFile: (filePath) => ipcRenderer.invoke('excel-read-file', filePath),

    // إزالة المستمعين
    removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
});

// وظائف مساعدة للتطبيق
contextBridge.exposeInMainWorld('appUtils', {
    // معلومات النظام
    platform: process.platform,
    arch: process.arch,
    nodeVersion: process.versions.node,
    electronVersion: process.versions.electron,
    chromeVersion: process.versions.chrome,
    
    // وظائف التاريخ والوقت
    formatDate: (date) => {
        return new Intl.DateTimeFormat('ar-SA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        }).format(new Date(date));
    },
    
    formatTime: (date) => {
        return new Intl.DateTimeFormat('ar-SA', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        }).format(new Date(date));
    },
    
    // وظائف التحقق
    isOnline: () => navigator.onLine,
    
    // وظائف الذاكرة
    getMemoryUsage: () => process.memoryUsage(),
    
    // وظائف البيئة
    isDevelopment: () => process.env.NODE_ENV === 'development',
    isProduction: () => process.env.NODE_ENV === 'production'
});

// إعداد معالجات الأحداث
window.addEventListener('DOMContentLoaded', () => {
    // إضافة معلومات التطبيق إلى DOM
    const appInfo = document.createElement('div');
    appInfo.id = 'app-info';
    appInfo.style.display = 'none';
    appInfo.dataset.platform = process.platform;
    appInfo.dataset.arch = process.arch;
    appInfo.dataset.electron = process.versions.electron;
    document.body.appendChild(appInfo);
    
    // إضافة فئة CSS للمنصة
    document.body.classList.add(`platform-${process.platform}`);
    
    // إعداد معالجات اختصارات لوحة المفاتيح
    document.addEventListener('keydown', (event) => {
        // Ctrl+Shift+I - أدوات المطور
        if (event.ctrlKey && event.shiftKey && event.key === 'I') {
            ipcRenderer.send('toggle-dev-tools');
        }
        
        // F11 - ملء الشاشة
        if (event.key === 'F11') {
            ipcRenderer.send('toggle-fullscreen');
        }
        
        // Ctrl+R - إعادة تحميل
        if (event.ctrlKey && event.key === 'r') {
            event.preventDefault();
            ipcRenderer.send('reload-app');
        }
        
        // Alt+F4 - إغلاق التطبيق (Windows)
        if (event.altKey && event.key === 'F4' && process.platform === 'win32') {
            event.preventDefault();
            ipcRenderer.send('close-app');
        }
    });
    
    // معالج تغيير حالة الاتصال
    window.addEventListener('online', () => {
        document.body.classList.remove('offline');
        document.body.classList.add('online');
    });
    
    window.addEventListener('offline', () => {
        document.body.classList.remove('online');
        document.body.classList.add('offline');
    });
    
    // تعيين الحالة الأولية
    if (navigator.onLine) {
        document.body.classList.add('online');
    } else {
        document.body.classList.add('offline');
    }
});

// معالج الأخطاء العامة
window.addEventListener('error', (event) => {
    console.error('خطأ في التطبيق:', event.error);
    
    // إرسال تقرير الخطأ إلى العملية الرئيسية
    ipcRenderer.send('app-error', {
        message: event.error.message,
        stack: event.error.stack,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
    });
});

// معالج الأخطاء غير المعالجة
window.addEventListener('unhandledrejection', (event) => {
    console.error('Promise مرفوض غير معالج:', event.reason);
    
    // إرسال تقرير الخطأ إلى العملية الرئيسية
    ipcRenderer.send('unhandled-rejection', {
        reason: event.reason,
        promise: event.promise
    });
});

console.log('تم تحميل Preload Script بنجاح');
