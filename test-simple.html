<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #2980b9;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
    </style>
</head>
<body>
    <h1>اختبار بسيط للأزرار</h1>
    
    <button class="btn" onclick="testBasic()">اختبار أساسي</button>
    <button class="btn" onclick="testNotification()">اختبار الإشعار</button>
    <button class="btn" onclick="testXLSX()">اختبار XLSX</button>
    <button class="btn" onclick="testImportExcel()">اختبار استيراد Excel</button>
    
    <div id="result" class="result">
        انقر على أي زر لاختباره...
    </div>

    <!-- تحميل الملفات بالترتيب الصحيح -->
    <script src="js/xlsx.min.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/excel-electron.js"></script>
    <script src="js/database.js"></script>
    <script src="js/students.js"></script>
    <script src="js/app.js"></script>

    <script>
        function updateResult(message) {
            document.getElementById('result').innerHTML = message;
        }

        function testBasic() {
            updateResult('✅ الزر الأساسي يعمل بنجاح!');
        }

        function testNotification() {
            if (typeof showNotification === 'function') {
                showNotification('هذا اختبار للإشعار!', 'success');
                updateResult('✅ دالة showNotification تعمل بنجاح!');
            } else {
                updateResult('❌ دالة showNotification غير متاحة');
            }
        }

        function testXLSX() {
            if (typeof XLSX !== 'undefined') {
                updateResult(`✅ مكتبة XLSX محملة بنجاح! الإصدار: ${XLSX.version || 'غير محدد'}`);
            } else {
                updateResult('❌ مكتبة XLSX غير محملة');
            }
        }

        function testImportExcel() {
            if (typeof importExcel === 'function') {
                updateResult('✅ دالة importExcel متاحة! (لن يتم تشغيلها في الاختبار)');
            } else {
                updateResult('❌ دالة importExcel غير متاحة');
            }
        }

        // اختبار تلقائي عند تحميل الصفحة
        window.onload = function() {
            setTimeout(() => {
                let status = [];
                
                // فحص الدوال الأساسية
                status.push(`showNotification: ${typeof showNotification === 'function' ? '✅' : '❌'}`);
                status.push(`showSection: ${typeof showSection === 'function' ? '✅' : '❌'}`);
                status.push(`closeModal: ${typeof closeModal === 'function' ? '✅' : '❌'}`);
                status.push(`XLSX: ${typeof XLSX !== 'undefined' ? '✅' : '❌'}`);
                status.push(`importExcel: ${typeof importExcel === 'function' ? '✅' : '❌'}`);
                status.push(`studentsManager: ${typeof studentsManager !== 'undefined' ? '✅' : '❌'}`);
                
                updateResult(`<strong>حالة النظام:</strong><br>${status.join('<br>')}`);
            }, 500);
        };
    </script>
</body>
</html>
