<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Excel في Electron</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn-success {
            background: #27ae60;
        }
        .btn-success:hover {
            background: #229954;
        }
        .btn-warning {
            background: #f39c12;
        }
        .btn-warning:hover {
            background: #e67e22;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .environment-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .environment-info h4 {
            margin-top: 0;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار وظائف Excel في Electron</h1>
        
        <div class="environment-info">
            <h4>معلومات البيئة</h4>
            <div id="environment-details">جاري التحميل...</div>
        </div>

        <div class="test-section">
            <h3>1. اختبار بيئة Electron</h3>
            <button class="btn" onclick="testElectronEnvironment()">فحص بيئة Electron</button>
            <div id="electron-status"></div>
        </div>

        <div class="test-section">
            <h3>2. اختبار Electron Excel Manager</h3>
            <button class="btn" onclick="testElectronExcelManager()">اختبار Excel Manager</button>
            <div id="manager-status"></div>
        </div>

        <div class="test-section">
            <h3>3. اختبار حفظ ملف Excel</h3>
            <button class="btn btn-success" onclick="testSaveExcelFile()">حفظ ملف تجريبي</button>
            <div id="save-status"></div>
        </div>

        <div class="test-section">
            <h3>4. اختبار قراءة ملف Excel</h3>
            <button class="btn btn-warning" onclick="testReadExcelFile()">قراءة ملف Excel</button>
            <div id="read-status"></div>
        </div>

        <div class="test-section">
            <h3>5. اختبار Electron APIs</h3>
            <button class="btn" onclick="testElectronAPIs()">اختبار APIs</button>
            <div id="apis-status"></div>
        </div>

        <div class="test-section">
            <h3>6. اختبار شامل</h3>
            <button class="btn btn-success" onclick="runFullTest()">تشغيل اختبار شامل</button>
            <div id="full-test-status"></div>
        </div>
    </div>

    <script src="js/xlsx.min.js"></script>
    <script src="js/excel-electron.js"></script>
    <script>
        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function updateEnvironmentInfo() {
            const details = document.getElementById('environment-details');
            let info = '';
            
            // معلومات أساسية
            info += `<strong>المتصفح:</strong> ${navigator.userAgent}<br>`;
            info += `<strong>اللغة:</strong> ${navigator.language}<br>`;
            
            // معلومات Electron
            if (typeof window.process !== 'undefined') {
                info += `<strong>Node.js:</strong> ${window.process.versions.node}<br>`;
                info += `<strong>Electron:</strong> ${window.process.versions.electron}<br>`;
                info += `<strong>Chrome:</strong> ${window.process.versions.chrome}<br>`;
                info += `<strong>النظام:</strong> ${window.process.platform}<br>`;
                info += `<strong>المعمارية:</strong> ${window.process.arch}<br>`;
            }
            
            // معلومات التطبيق
            if (typeof window.electronAPI !== 'undefined') {
                info += `<strong>Electron API:</strong> متاح<br>`;
            }
            
            if (typeof electronExcelManager !== 'undefined') {
                info += `<strong>Excel Manager:</strong> متاح<br>`;
            }
            
            details.innerHTML = info;
        }

        function testElectronEnvironment() {
            let message = '';
            let type = 'info';
            
            if (typeof window.process !== 'undefined' && window.process.type === 'renderer') {
                message = '✅ تم تشغيل التطبيق في بيئة Electron بنجاح!';
                type = 'success';
            } else {
                message = '❌ التطبيق يعمل في المتصفح العادي، ليس في Electron';
                type = 'error';
            }
            
            showStatus('electron-status', message, type);
        }

        function testElectronExcelManager() {
            if (typeof electronExcelManager !== 'undefined') {
                const isElectron = electronExcelManager.isElectron;
                const message = isElectron ? 
                    '✅ Excel Manager يعمل في بيئة Electron' : 
                    '⚠️ Excel Manager يعمل في وضع المتصفح';
                const type = isElectron ? 'success' : 'info';
                showStatus('manager-status', message, type);
            } else {
                showStatus('manager-status', '❌ Excel Manager غير متاح', 'error');
            }
        }

        async function testSaveExcelFile() {
            try {
                if (typeof XLSX === 'undefined') {
                    showStatus('save-status', '❌ مكتبة XLSX غير محملة', 'error');
                    return;
                }

                // إنشاء بيانات تجريبية
                const testData = [
                    ['الاسم', 'العمر', 'المدينة', 'التقدير'],
                    ['أحمد محمد', 25, 'الرياض', 'ممتاز'],
                    ['فاطمة علي', 23, 'جدة', 'جيد جداً'],
                    ['محمد سالم', 27, 'الدمام', 'جيد']
                ];

                const wb = XLSX.utils.book_new();
                const ws = XLSX.utils.aoa_to_sheet(testData);
                XLSX.utils.book_append_sheet(wb, ws, 'البيانات التجريبية');

                if (typeof electronExcelManager !== 'undefined') {
                    const result = await electronExcelManager.saveExcelFile(wb, 'اختبار_Electron.xlsx');
                    if (result.success && !result.canceled) {
                        showStatus('save-status', '✅ تم حفظ الملف بنجاح باستخدام Electron!', 'success');
                    } else if (result.canceled) {
                        showStatus('save-status', '⚠️ تم إلغاء العملية', 'info');
                    }
                } else {
                    XLSX.writeFile(wb, 'اختبار_متصفح.xlsx');
                    showStatus('save-status', '✅ تم حفظ الملف باستخدام المتصفح', 'success');
                }
            } catch (error) {
                showStatus('save-status', '❌ خطأ في حفظ الملف: ' + error.message, 'error');
            }
        }

        async function testReadExcelFile() {
            try {
                if (typeof electronExcelManager !== 'undefined') {
                    const result = await electronExcelManager.readExcelFile();
                    if (result.success && !result.canceled) {
                        const data = electronExcelManager.extractDataFromWorkbook(result.workbook);
                        showStatus('read-status', `✅ تم قراءة الملف بنجاح! عدد الصفوف: ${data.length}`, 'success');
                    } else if (result.canceled) {
                        showStatus('read-status', '⚠️ تم إلغاء العملية', 'info');
                    }
                } else {
                    showStatus('read-status', '⚠️ يرجى استخدام input file في المتصفح', 'info');
                }
            } catch (error) {
                showStatus('read-status', '❌ خطأ في قراءة الملف: ' + error.message, 'error');
            }
        }

        async function testElectronAPIs() {
            let message = '';
            let type = 'info';
            
            if (typeof window.electronAPI !== 'undefined') {
                try {
                    const version = await window.electronAPI.getAppVersion();
                    message = `✅ Electron APIs تعمل بنجاح! إصدار التطبيق: ${version}`;
                    type = 'success';
                } catch (error) {
                    message = `❌ خطأ في Electron APIs: ${error.message}`;
                    type = 'error';
                }
            } else {
                message = '❌ Electron APIs غير متاحة';
                type = 'error';
            }
            
            showStatus('apis-status', message, type);
        }

        async function runFullTest() {
            showStatus('full-test-status', '🔄 جاري تشغيل الاختبار الشامل...', 'info');
            
            let results = [];
            
            // اختبار البيئة
            const isElectron = typeof window.process !== 'undefined' && window.process.type === 'renderer';
            results.push(`البيئة: ${isElectron ? 'Electron ✅' : 'متصفح ⚠️'}`);
            
            // اختبار XLSX
            const hasXLSX = typeof XLSX !== 'undefined';
            results.push(`مكتبة XLSX: ${hasXLSX ? 'محملة ✅' : 'غير محملة ❌'}`);
            
            // اختبار Excel Manager
            const hasManager = typeof electronExcelManager !== 'undefined';
            results.push(`Excel Manager: ${hasManager ? 'متاح ✅' : 'غير متاح ❌'}`);
            
            // اختبار Electron APIs
            const hasAPIs = typeof window.electronAPI !== 'undefined';
            results.push(`Electron APIs: ${hasAPIs ? 'متاحة ✅' : 'غير متاحة ❌'}`);
            
            const summary = results.join('<br>');
            const allPassed = isElectron && hasXLSX && hasManager && hasAPIs;
            
            showStatus('full-test-status', 
                `<strong>نتائج الاختبار الشامل:</strong><br>${summary}<br><br>` +
                `<strong>النتيجة النهائية:</strong> ${allPassed ? '✅ جميع الاختبارات نجحت!' : '⚠️ بعض الاختبارات فشلت'}`,
                allPassed ? 'success' : 'info'
            );
        }

        // تشغيل تلقائي عند تحميل الصفحة
        window.onload = function() {
            updateEnvironmentInfo();
            testElectronEnvironment();
            testElectronExcelManager();
        };
    </script>
</body>
</html>
