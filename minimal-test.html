<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أساسي</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #2980b9;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
    </style>
</head>
<body>
    <h1>اختبار أساسي للأزرار</h1>
    
    <button class="btn" onclick="test1()">اختبار 1</button>
    <button class="btn" onclick="test2()">اختبار 2</button>
    <button class="btn" onclick="loadOneFile()">تحميل ملف واحد</button>
    <button class="btn" onclick="loadAllFiles()">تحميل جميع الملفات</button>
    
    <div id="result" class="result">
        انقر على أي زر لاختباره...
    </div>

    <script>
        function updateResult(message) {
            document.getElementById('result').innerHTML = message;
        }

        function test1() {
            updateResult('✅ الزر الأول يعمل بنجاح!');
        }

        function test2() {
            updateResult('✅ الزر الثاني يعمل بنجاح!');
        }

        function loadOneFile() {
            updateResult('🔄 جاري تحميل ملف واحد...');
            
            const script = document.createElement('script');
            script.src = 'js/utils.js';
            script.onload = () => {
                updateResult('✅ تم تحميل utils.js بنجاح!');
                
                // اختبار الدالة
                if (typeof showNotification === 'function') {
                    showNotification('اختبار الإشعار!', 'success');
                    updateResult('✅ utils.js محمل ودالة showNotification تعمل!');
                } else {
                    updateResult('❌ utils.js محمل لكن showNotification غير متاحة');
                }
            };
            script.onerror = () => {
                updateResult('❌ فشل في تحميل utils.js');
            };
            document.head.appendChild(script);
        }

        function loadAllFiles() {
            updateResult('🔄 جاري تحميل جميع الملفات...');
            
            const files = [
                'js/xlsx.min.js',
                'js/utils.js',
                'js/database.js',
                'js/students.js',
                'js/app.js'
            ];
            
            let loadedCount = 0;
            let errors = [];
            
            files.forEach((file, index) => {
                const script = document.createElement('script');
                script.src = file;
                
                script.onload = () => {
                    loadedCount++;
                    updateResult(`✅ تم تحميل ${file} (${loadedCount}/${files.length})`);
                    
                    if (loadedCount === files.length) {
                        setTimeout(() => {
                            testLoadedFunctions();
                        }, 1000);
                    }
                };
                
                script.onerror = () => {
                    errors.push(file);
                    updateResult(`❌ فشل في تحميل ${file}`);
                };
                
                document.head.appendChild(script);
            });
        }

        function testLoadedFunctions() {
            let results = [];
            
            // اختبار الدوال
            const functions = [
                'showNotification',
                'showSection',
                'closeModal',
                'importExcel'
            ];
            
            functions.forEach(func => {
                if (typeof window[func] === 'function') {
                    results.push(`✅ ${func}`);
                } else {
                    results.push(`❌ ${func}`);
                }
            });
            
            // اختبار المتغيرات
            const variables = [
                'XLSX',
                'studentsManager',
                'dbManager'
            ];
            
            variables.forEach(variable => {
                if (typeof window[variable] !== 'undefined') {
                    results.push(`✅ ${variable}`);
                } else {
                    results.push(`❌ ${variable}`);
                }
            });
            
            updateResult(`<strong>نتائج الاختبار:</strong><br>${results.join('<br>')}`);
        }

        // تسجيل الأخطاء
        window.addEventListener('error', function(e) {
            console.error('خطأ:', e.error);
            updateResult(`❌ خطأ JavaScript: ${e.error.message} في ${e.filename}:${e.lineno}`);
        });

        window.addEventListener('unhandledrejection', function(e) {
            console.error('Promise مرفوض:', e.reason);
            updateResult(`❌ Promise مرفوض: ${e.reason}`);
        });
    </script>
</body>
</html>
