// إدارة التقارير والإحصائيات
class ReportsManager {
    constructor() {
        this.chartInstances = {};
        this.init();
    }

    init() {
        // تحميل مكتبة Chart.js إذا لم تكن محملة
        this.loadChartJS();
    }

    loadChartJS() {
        if (typeof Chart === 'undefined') {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/chart.js';
            document.head.appendChild(script);
        }
    }

    async generateSemesterReport() {
        try {
            const reportContent = document.getElementById('report-content');
            if (!reportContent) return;

            // إظهار نافذة اختيار المعايير
            const criteria = await this.showReportCriteriaModal('semester');
            if (!criteria) return;

            // جمع البيانات
            const data = await this.collectSemesterData(criteria);
            
            // إنشاء التقرير
            const reportHTML = this.generateSemesterReportHTML(data, criteria);
            reportContent.innerHTML = reportHTML;

            // إظهار قسم التقارير
            showSection('reports');

            // إظهار أزرار التقرير
            const reportActions = document.getElementById('report-actions');
            if (reportActions) {
                reportActions.style.display = 'block';
            }
            
            showNotification('تم إنشاء التقرير الفصلي بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في إنشاء التقرير الفصلي:', error);
            showNotification('خطأ في إنشاء التقرير الفصلي', 'error');
        }
    }

    async generateAnnualReport() {
        try {
            const reportContent = document.getElementById('report-content');
            if (!reportContent) return;

            // إظهار نافذة اختيار المعايير
            const criteria = await this.showReportCriteriaModal('annual');
            if (!criteria) return;

            // جمع البيانات
            const data = await this.collectAnnualData(criteria);
            
            // إنشاء التقرير
            const reportHTML = this.generateAnnualReportHTML(data, criteria);
            reportContent.innerHTML = reportHTML;

            // إظهار قسم التقارير
            showSection('reports');

            // إظهار أزرار التقرير
            const reportActions = document.getElementById('report-actions');
            if (reportActions) {
                reportActions.style.display = 'block';
            }
            
            showNotification('تم إنشاء التقرير السنوي بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في إنشاء التقرير السنوي:', error);
            showNotification('خطأ في إنشاء التقرير السنوي', 'error');
        }
    }

    showReportCriteriaModal(reportType) {
        return new Promise((resolve) => {
            const modalContent = `
                <div class="modal-header">
                    <h3>${reportType === 'semester' ? 'معايير التقرير الفصلي' : 'معايير التقرير السنوي'}</h3>
                    <button class="btn btn-sm btn-danger" onclick="closeModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form id="report-criteria-form" class="criteria-form">
                    <div class="form-group">
                        <label for="report-academic-year">العام الدراسي:</label>
                        <select id="report-academic-year" required>
                            <option value="2024-2025">2024-2025</option>
                            <option value="2023-2024">2023-2024</option>
                        </select>
                    </div>
                    
                    ${reportType === 'semester' ? `
                    <div class="form-group">
                        <label for="report-semester">الفصل الدراسي:</label>
                        <select id="report-semester" required>
                            <option value="1">الفصل الأول</option>
                            <option value="2">الفصل الثاني</option>
                        </select>
                    </div>
                    ` : ''}
                    
                    <div class="form-group">
                        <label for="report-grade">الصف (اختياري):</label>
                        <select id="report-grade">
                            <option value="">جميع الصفوف</option>
                            <option value="1">الصف الأول</option>
                            <option value="2">الصف الثاني</option>
                            <option value="3">الصف الثالث</option>
                            <option value="4">الصف الرابع</option>
                            <option value="5">الصف الخامس</option>
                            <option value="6">الصف السادس</option>
                            <option value="7">الصف السابع</option>
                            <option value="8">الصف الثامن</option>
                            <option value="9">الصف التاسع</option>
                            <option value="10">الصف العاشر</option>
                            <option value="11">الصف الحادي عشر</option>
                            <option value="12">الصف الثاني عشر</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="report-section">الشعبة (اختياري):</label>
                        <select id="report-section">
                            <option value="">جميع الشعب</option>
                            <option value="أ">أ</option>
                            <option value="ب">ب</option>
                            <option value="ج">ج</option>
                            <option value="د">د</option>
                        </select>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-file-alt"></i> إنشاء التقرير
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal()">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </form>
            `;

            showModal(modalContent);

            document.getElementById('report-criteria-form').addEventListener('submit', (e) => {
                e.preventDefault();
                
                const criteria = {
                    academic_year: document.getElementById('report-academic-year').value,
                    grade: document.getElementById('report-grade').value,
                    section: document.getElementById('report-section').value
                };

                if (reportType === 'semester') {
                    criteria.semester = document.getElementById('report-semester').value;
                }

                closeModal();
                resolve(criteria);
            });
        });
    }

    async collectSemesterData(criteria) {
        const filters = {
            academic_year: criteria.academic_year,
            semester: criteria.semester
        };

        if (criteria.grade) filters.grade_level = criteria.grade;

        const grades = await dbManager.getGrades(filters);
        const students = await dbManager.getStudents({
            grade: criteria.grade,
            section: criteria.section
        });

        const statistics = await dbManager.getStatistics(filters);

        return {
            grades,
            students,
            statistics,
            criteria
        };
    }

    async collectAnnualData(criteria) {
        const semester1Data = await this.collectSemesterData({
            ...criteria,
            semester: '1'
        });

        const semester2Data = await this.collectSemesterData({
            ...criteria,
            semester: '2'
        });

        return {
            semester1: semester1Data,
            semester2: semester2Data,
            criteria
        };
    }

    generateSemesterReportHTML(data, criteria) {
        const { grades, students, statistics } = data;
        
        return `
            <div class="print-header">
                <div class="school-logo">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <h1>استمارة تقويم تقنية المعلومات</h1>
                <div class="school-info">التقرير الفصلي - وزارة التربية والتعليم</div>
                <div class="report-info">
                    <span>العام الدراسي: ${criteria.academic_year}</span>
                    <span>الفصل الدراسي: ${criteria.semester === '1' ? 'الأول' : 'الثاني'}</span>
                    <span>تاريخ إنشاء التقرير: ${formatDateArabic(new Date())}</span>
                    <span>الوقت: ${formatTime(new Date())}</span>
                </div>
                <div class="report-subtitle">
                    تقرير شامل لأداء الطلاب في مادة تقنية المعلومات
                    ${criteria.grade ? ` - الصف ${criteria.grade}` : ''}
                    ${criteria.section ? ` - الشعبة ${criteria.section}` : ''}
                </div>
            </div>

            <div class="report-title">
                <h2>التقرير الفصلي - ${criteria.semester === '1' ? 'الفصل الأول' : 'الفصل الثاني'}</h2>
                <div class="report-details">
                    العام الدراسي: ${criteria.academic_year}
                    ${criteria.grade ? ` - الصف: ${criteria.grade}` : ''}
                    ${criteria.section ? ` - الشعبة: ${criteria.section}` : ''}
                </div>
            </div>

            <div class="statistics-section">
                <h3>الإحصائيات العامة</h3>
                <div class="statistics-grid">
                    <div class="stat-card">
                        <h4>إجمالي الطلاب</h4>
                        <div class="stat-number">${statistics.total_students}</div>
                    </div>
                    <div class="stat-card">
                        <h4>المتوسط العام</h4>
                        <div class="stat-number">${statistics.average_score.toFixed(2)}</div>
                    </div>
                    <div class="stat-card">
                        <h4>نسبة النجاح</h4>
                        <div class="stat-number">${statistics.pass_rate.toFixed(1)}%</div>
                    </div>
                </div>
            </div>

            <div class="grade-distribution-section">
                <h3>توزيع الدرجات</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>التقدير</th>
                            <th>عدد الطلاب</th>
                            <th>النسبة المئوية</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="grade-excellent">
                            <td>ممتاز (أ)</td>
                            <td>${statistics.grade_distribution.excellent}</td>
                            <td>${((statistics.grade_distribution.excellent / statistics.total_students) * 100).toFixed(1)}%</td>
                        </tr>
                        <tr class="grade-very-good">
                            <td>جيد جداً (ب)</td>
                            <td>${statistics.grade_distribution.very_good}</td>
                            <td>${((statistics.grade_distribution.very_good / statistics.total_students) * 100).toFixed(1)}%</td>
                        </tr>
                        <tr class="grade-good">
                            <td>جيد (ج)</td>
                            <td>${statistics.grade_distribution.good}</td>
                            <td>${((statistics.grade_distribution.good / statistics.total_students) * 100).toFixed(1)}%</td>
                        </tr>
                        <tr class="grade-acceptable">
                            <td>مقبول (د)</td>
                            <td>${statistics.grade_distribution.acceptable}</td>
                            <td>${((statistics.grade_distribution.acceptable / statistics.total_students) * 100).toFixed(1)}%</td>
                        </tr>
                        <tr class="grade-weak">
                            <td>ضعيف (هـ)</td>
                            <td>${statistics.grade_distribution.weak}</td>
                            <td>${((statistics.grade_distribution.weak / statistics.total_students) * 100).toFixed(1)}%</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="detailed-grades-section">
                <h3>تفاصيل الدرجات</h3>
                ${this.generateDetailedGradesTable(grades, students)}
            </div>

            <div class="print-footer">
                <div class="footer-info">
                    <div class="report-summary-footer">
                        <p><strong>ملاحظات هامة:</strong></p>
                        <ul>
                            <li>هذا التقرير تم إنشاؤه آلياً بواسطة نظام تقويم تقنية المعلومات</li>
                            <li>جميع البيانات والدرجات مطابقة لسجلات النظام الرسمية</li>
                            <li>في حالة وجود أي استفسار، يرجى مراجعة إدارة المدرسة</li>
                        </ul>
                    </div>
                </div>

                <div class="signature-section">
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <div class="signature-label">معلم المادة</div>
                        <div class="signature-title">الاسم والتوقيع والتاريخ</div>
                        <div class="signature-date">التاريخ: ___/___/______</div>
                    </div>
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <div class="signature-label">رئيس قسم تقنية المعلومات</div>
                        <div class="signature-title">الاسم والتوقيع والتاريخ</div>
                        <div class="signature-date">التاريخ: ___/___/______</div>
                    </div>
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <div class="signature-label">مدير المدرسة</div>
                        <div class="signature-title">الاسم والتوقيع والختم</div>
                        <div class="signature-date">التاريخ: ___/___/______</div>
                    </div>
                </div>

                <div class="footer-stamp">
                    <div class="official-stamp">
                        <div class="stamp-border">
                            <div class="stamp-content">
                                <div class="stamp-text">ختم المدرسة الرسمي</div>
                                <div class="stamp-date">${formatDateArabic(new Date())}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="report-actions no-print">
                <div class="print-options-section">
                    <h4><i class="fas fa-print"></i> خيارات الطباعة الاحترافية</h4>
                    <div class="print-buttons-grid">
                        <button class="btn btn-primary print-btn" onclick="reportsManager.printReport('color')">
                            <i class="fas fa-palette"></i> طباعة ملونة عالية الجودة
                        </button>
                        <button class="btn btn-secondary print-btn" onclick="reportsManager.printReport('bw')">
                            <i class="fas fa-adjust"></i> طباعة أبيض وأسود
                        </button>
                        <button class="btn btn-info print-btn" onclick="reportsManager.printReport('official')">
                            <i class="fas fa-stamp"></i> طباعة رسمية مع التوقيعات
                        </button>
                        <button class="btn btn-warning print-btn" onclick="reportsManager.printReport('summary')">
                            <i class="fas fa-list"></i> طباعة ملخص تنفيذي
                        </button>
                    </div>
                </div>

                <div class="export-options-section">
                    <h4><i class="fas fa-download"></i> تصدير التقرير</h4>
                    <div class="export-buttons-grid">
                        <button class="btn btn-success" onclick="reportsManager.exportReportToPDF()">
                            <i class="fas fa-file-pdf"></i> تصدير PDF احترافي
                        </button>
                        <button class="btn btn-warning" onclick="reportsManager.exportReportToExcel()">
                            <i class="fas fa-file-excel"></i> تصدير Excel مفصل
                        </button>
                        <button class="btn btn-info" onclick="reportsManager.exportReportToWord()">
                            <i class="fas fa-file-word"></i> تصدير Word قابل للتعديل
                        </button>
                    </div>
                </div>

                <div class="preview-options-section">
                    <h4><i class="fas fa-eye"></i> معاينة وإعدادات</h4>
                    <div class="preview-buttons-grid">
                        <button class="btn btn-outline-primary" onclick="reportsManager.showPrintPreview()">
                            <i class="fas fa-search"></i> معاينة قبل الطباعة
                        </button>
                        <button class="btn btn-outline-secondary" onclick="reportsManager.togglePrintMode()">
                            <i class="fas fa-toggle-on"></i> وضع الطباعة
                        </button>
                        <button class="btn btn-outline-info" onclick="reportsManager.printSettings()">
                            <i class="fas fa-cog"></i> إعدادات الطباعة
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    generateAnnualReportHTML(data, criteria) {
        const { semester1, semester2 } = data;
        
        return `
            <div class="print-header">
                <div class="school-logo">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <h1>استمارة تقويم تقنية المعلومات</h1>
                <div class="school-info">التقرير السنوي الشامل - وزارة التربية والتعليم</div>
                <div class="report-info">
                    <span>العام الدراسي: ${criteria.academic_year}</span>
                    <span>تاريخ إنشاء التقرير: ${formatDateArabic(new Date())}</span>
                    <span>الوقت: ${formatTime(new Date())}</span>
                    <span>نوع التقرير: سنوي مقارن</span>
                </div>
                <div class="report-subtitle">
                    تقرير مقارن شامل لأداء الطلاب عبر الفصلين الدراسيين
                    ${criteria.grade ? ` - الصف ${criteria.grade}` : ''}
                    ${criteria.section ? ` - الشعبة ${criteria.section}` : ''}
                </div>
            </div>

            <div class="report-title">
                <h2>التقرير السنوي</h2>
                <div class="report-details">
                    العام الدراسي: ${criteria.academic_year}
                    ${criteria.grade ? ` - الصف: ${criteria.grade}` : ''}
                    ${criteria.section ? ` - الشعبة: ${criteria.section}` : ''}
                </div>
            </div>

            <div class="annual-comparison">
                <h3>مقارنة الفصلين الدراسيين</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>البيان</th>
                            <th>الفصل الأول</th>
                            <th>الفصل الثاني</th>
                            <th>التحسن</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>عدد الطلاب</td>
                            <td>${semester1.statistics.total_students}</td>
                            <td>${semester2.statistics.total_students}</td>
                            <td>${semester2.statistics.total_students - semester1.statistics.total_students}</td>
                        </tr>
                        <tr>
                            <td>المتوسط العام</td>
                            <td>${semester1.statistics.average_score.toFixed(2)}</td>
                            <td>${semester2.statistics.average_score.toFixed(2)}</td>
                            <td>${(semester2.statistics.average_score - semester1.statistics.average_score).toFixed(2)}</td>
                        </tr>
                        <tr>
                            <td>نسبة النجاح</td>
                            <td>${semester1.statistics.pass_rate.toFixed(1)}%</td>
                            <td>${semester2.statistics.pass_rate.toFixed(1)}%</td>
                            <td>${(semester2.statistics.pass_rate - semester1.statistics.pass_rate).toFixed(1)}%</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="semester-details">
                <div class="semester-section">
                    <h3>الفصل الدراسي الأول</h3>
                    ${this.generateSemesterSummary(semester1)}
                </div>
                
                <div class="semester-section">
                    <h3>الفصل الدراسي الثاني</h3>
                    ${this.generateSemesterSummary(semester2)}
                </div>
            </div>

            <div class="print-footer">
                <div class="signature-section">
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <div class="signature-label">معلم المادة</div>
                        <div class="signature-title">الاسم والتوقيع</div>
                    </div>
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <div class="signature-label">رئيس القسم</div>
                        <div class="signature-title">الاسم والتوقيع</div>
                    </div>
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <div class="signature-label">مدير المدرسة</div>
                        <div class="signature-title">الاسم والتوقيع</div>
                    </div>
                </div>
            </div>

            <div class="report-actions no-print">
                <div class="print-options-section">
                    <h4><i class="fas fa-print"></i> خيارات الطباعة الاحترافية</h4>
                    <div class="print-buttons-grid">
                        <button class="btn btn-primary print-btn" onclick="reportsManager.printReport('color')">
                            <i class="fas fa-palette"></i> طباعة ملونة عالية الجودة
                        </button>
                        <button class="btn btn-secondary print-btn" onclick="reportsManager.printReport('bw')">
                            <i class="fas fa-adjust"></i> طباعة أبيض وأسود
                        </button>
                        <button class="btn btn-info print-btn" onclick="reportsManager.printReport('official')">
                            <i class="fas fa-stamp"></i> طباعة رسمية مع التوقيعات
                        </button>
                        <button class="btn btn-warning print-btn" onclick="reportsManager.printReport('summary')">
                            <i class="fas fa-list"></i> طباعة ملخص تنفيذي
                        </button>
                    </div>
                </div>

                <div class="export-options-section">
                    <h4><i class="fas fa-download"></i> تصدير التقرير</h4>
                    <div class="export-buttons-grid">
                        <button class="btn btn-success" onclick="reportsManager.exportReportToPDF()">
                            <i class="fas fa-file-pdf"></i> تصدير PDF احترافي
                        </button>
                        <button class="btn btn-warning" onclick="reportsManager.exportReportToExcel()">
                            <i class="fas fa-file-excel"></i> تصدير Excel مفصل
                        </button>
                        <button class="btn btn-info" onclick="reportsManager.exportReportToWord()">
                            <i class="fas fa-file-word"></i> تصدير Word قابل للتعديل
                        </button>
                    </div>
                </div>

                <div class="preview-options-section">
                    <h4><i class="fas fa-eye"></i> معاينة وإعدادات</h4>
                    <div class="preview-buttons-grid">
                        <button class="btn btn-outline-primary" onclick="reportsManager.showPrintPreview()">
                            <i class="fas fa-search"></i> معاينة قبل الطباعة
                        </button>
                        <button class="btn btn-outline-secondary" onclick="reportsManager.togglePrintMode()">
                            <i class="fas fa-toggle-on"></i> وضع الطباعة
                        </button>
                        <button class="btn btn-outline-info" onclick="reportsManager.printSettings()">
                            <i class="fas fa-cog"></i> إعدادات الطباعة
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    generateSemesterSummary(semesterData) {
        const { statistics } = semesterData;
        
        return `
            <div class="semester-summary">
                <div class="summary-stats">
                    <div class="stat-item">
                        <span class="stat-label">إجمالي الطلاب:</span>
                        <span class="stat-value">${statistics.total_students}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">المتوسط العام:</span>
                        <span class="stat-value">${statistics.average_score.toFixed(2)}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">نسبة النجاح:</span>
                        <span class="stat-value">${statistics.pass_rate.toFixed(1)}%</span>
                    </div>
                </div>
                
                <div class="grade-breakdown">
                    <div class="grade-item grade-excellent">
                        <span>ممتاز: ${statistics.grade_distribution.excellent}</span>
                    </div>
                    <div class="grade-item grade-very-good">
                        <span>جيد جداً: ${statistics.grade_distribution.very_good}</span>
                    </div>
                    <div class="grade-item grade-good">
                        <span>جيد: ${statistics.grade_distribution.good}</span>
                    </div>
                    <div class="grade-item grade-acceptable">
                        <span>مقبول: ${statistics.grade_distribution.acceptable}</span>
                    </div>
                    <div class="grade-item grade-weak">
                        <span>ضعيف: ${statistics.grade_distribution.weak}</span>
                    </div>
                </div>
            </div>
        `;
    }

    generateDetailedGradesTable(grades, students) {
        if (grades.length === 0) {
            return '<p class="text-center">لا توجد درجات مسجلة</p>';
        }

        let tableHTML = `
            <table class="data-table">
                <thead>
                    <tr>
                        <th>الرقم</th>
                        <th>اسم الطالب</th>
                        <th>المجموع</th>
                        <th>المستوى</th>
                        <th>التقدير</th>
                    </tr>
                </thead>
                <tbody>
        `;

        grades.forEach(grade => {
            const student = students.find(s => s.id === grade.student_id);
            if (student) {
                tableHTML += `
                    <tr class="${this.getGradeCSSClass(grade.level)}">
                        <td>${student.student_number}</td>
                        <td>${student.name}</td>
                        <td>${grade.total.toFixed(2)}</td>
                        <td>${grade.level}</td>
                        <td>${grade.descriptive_phrase}</td>
                    </tr>
                `;
            }
        });

        tableHTML += `
                </tbody>
            </table>
        `;

        return tableHTML;
    }

    getGradeCSSClass(level) {
        if (level === 'أ' || level === '1') return 'grade-excellent';
        if (level === 'ب' || level === '2') return 'grade-very-good';
        if (level === 'ج' || level === '3') return 'grade-good';
        if (level === 'د' || level === '4') return 'grade-acceptable';
        return 'grade-weak';
    }

    async showStatistics() {
        try {
            const statistics = await dbManager.getStatistics();
            const reportContent = document.getElementById('report-content');
            
            const statisticsHTML = `
                <div class="statistics-dashboard">
                    <h2>الإحصائيات والتحليلات</h2>
                    
                    <div class="stats-overview">
                        <div class="stat-card">
                            <h3>إجمالي الطلاب</h3>
                            <div class="stat-number">${statistics.total_students}</div>
                        </div>
                        <div class="stat-card">
                            <h3>المتوسط العام</h3>
                            <div class="stat-number">${statistics.average_score.toFixed(2)}</div>
                        </div>
                        <div class="stat-card">
                            <h3>نسبة النجاح</h3>
                            <div class="stat-number">${statistics.pass_rate.toFixed(1)}%</div>
                        </div>
                    </div>
                    
                    <div class="charts-section">
                        <div class="chart-container">
                            <canvas id="gradeDistributionChart"></canvas>
                        </div>
                    </div>
                </div>
            `;
            
            reportContent.innerHTML = statisticsHTML;
            
            // إنشاء الرسم البياني
            this.createGradeDistributionChart(statistics.grade_distribution);
            
            showSection('reports');
            showNotification('تم تحميل الإحصائيات بنجاح', 'success');
            
        } catch (error) {
            console.error('خطأ في تحميل الإحصائيات:', error);
            showNotification('خطأ في تحميل الإحصائيات', 'error');
        }
    }

    createGradeDistributionChart(distribution) {
        const ctx = document.getElementById('gradeDistributionChart');
        if (!ctx) return;

        if (this.chartInstances.gradeDistribution) {
            this.chartInstances.gradeDistribution.destroy();
        }

        this.chartInstances.gradeDistribution = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['ممتاز', 'جيد جداً', 'جيد', 'مقبول', 'ضعيف'],
                datasets: [{
                    data: [
                        distribution.excellent,
                        distribution.very_good,
                        distribution.good,
                        distribution.acceptable,
                        distribution.weak
                    ],
                    backgroundColor: [
                        '#27ae60',
                        '#3498db',
                        '#f39c12',
                        '#e67e22',
                        '#e74c3c'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'توزيع الدرجات'
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    showCharts() {
        // سيتم تطوير هذه الوظيفة لاحقاً
        showNotification('سيتم إضافة المزيد من الرسوم البيانية قريباً', 'info');
    }

    // وظائف الطباعة المتقدمة
    printReport(type = 'color') {
        const body = document.body;

        // إزالة جميع فئات الطباعة السابقة
        body.classList.remove('print-color', 'print-bw', 'print-official', 'print-summary');

        // إضافة فئة الطباعة المناسبة
        switch(type) {
            case 'color':
                body.classList.add('print-color');
                break;
            case 'bw':
                body.classList.add('print-bw');
                break;
            case 'official':
                body.classList.add('print-official');
                break;
            case 'summary':
                body.classList.add('print-summary');
                break;
        }

        // إضافة معلومات الطباعة
        this.addPrintMetadata(type);

        // طباعة التقرير
        setTimeout(() => {
            window.print();

            // إزالة فئات الطباعة بعد الطباعة
            setTimeout(() => {
                body.classList.remove('print-color', 'print-bw', 'print-official', 'print-summary');
                this.removePrintMetadata();
            }, 1000);
        }, 500);

        showNotification(`تم إعداد التقرير للطباعة ${this.getPrintTypeName(type)}`, 'success');
    }

    getPrintTypeName(type) {
        const names = {
            'color': 'الملونة',
            'bw': 'بالأبيض والأسود',
            'official': 'الرسمية',
            'summary': 'الملخص التنفيذي'
        };
        return names[type] || 'العادية';
    }

    addPrintMetadata(type) {
        // إضافة معلومات إضافية للطباعة
        const reportContent = document.getElementById('report-content');
        if (!reportContent) return;

        const metadata = document.createElement('div');
        metadata.className = 'print-metadata';
        metadata.innerHTML = `
            <div class="print-info-header">
                <div class="print-timestamp">تاريخ الطباعة: ${formatDateArabic(new Date())} - ${formatTime(new Date())}</div>
                <div class="print-type">نوع الطباعة: ${this.getPrintTypeName(type)}</div>
                <div class="print-user">طُبع بواسطة: نظام تقويم تقنية المعلومات</div>
            </div>
        `;

        reportContent.insertBefore(metadata, reportContent.firstChild);
    }

    removePrintMetadata() {
        const metadata = document.querySelector('.print-metadata');
        if (metadata) {
            metadata.remove();
        }
    }

    showPrintPreview() {
        const reportContent = document.getElementById('report-content');
        if (!reportContent) {
            showNotification('لا يوجد تقرير لمعاينته', 'warning');
            return;
        }

        // إنشاء نافذة معاينة
        const previewWindow = window.open('', '_blank', 'width=1000,height=700,scrollbars=yes');

        const previewHTML = `
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>معاينة التقرير</title>
                <link rel="stylesheet" href="css/styles.css">
                <link rel="stylesheet" href="css/print.css">
                <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
                <style>
                    body {
                        background: white;
                        padding: 2cm;
                        font-family: 'Noto Sans Arabic', Arial, sans-serif;
                    }
                    .preview-header {
                        text-align: center;
                        padding: 1rem;
                        background: #f8f9fa;
                        border: 2px solid #dee2e6;
                        border-radius: 8px;
                        margin-bottom: 2rem;
                    }
                    .preview-actions {
                        position: fixed;
                        top: 10px;
                        right: 10px;
                        z-index: 1000;
                    }
                    .preview-btn {
                        margin: 0 5px;
                        padding: 8px 16px;
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                        font-family: inherit;
                    }
                    .btn-print { background: #007bff; color: white; }
                    .btn-close { background: #6c757d; color: white; }
                </style>
            </head>
            <body>
                <div class="preview-actions">
                    <button class="preview-btn btn-print" onclick="window.print()">
                        طباعة
                    </button>
                    <button class="preview-btn btn-close" onclick="window.close()">
                        إغلاق
                    </button>
                </div>

                <div class="preview-header">
                    <h2>معاينة التقرير قبل الطباعة</h2>
                    <p>هذه معاينة لكيفية ظهور التقرير عند الطباعة</p>
                </div>

                ${reportContent.innerHTML}
            </body>
            </html>
        `;

        previewWindow.document.write(previewHTML);
        previewWindow.document.close();

        showNotification('تم فتح معاينة التقرير في نافذة جديدة', 'success');
    }

    togglePrintMode() {
        const body = document.body;
        const isInPrintMode = body.classList.contains('print-mode');

        if (isInPrintMode) {
            body.classList.remove('print-mode');
            showNotification('تم إلغاء وضع الطباعة', 'info');
        } else {
            body.classList.add('print-mode');
            showNotification('تم تفعيل وضع الطباعة - يمكنك الآن رؤية التقرير كما سيظهر عند الطباعة', 'success');
        }
    }

    printSettings() {
        const settingsModal = `
            <div class="modal-header">
                <h3>إعدادات الطباعة المتقدمة</h3>
                <button class="btn btn-sm btn-danger" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="print-settings-content">
                <div class="setting-group">
                    <h4>إعدادات الصفحة</h4>
                    <div class="setting-item">
                        <label>
                            <input type="radio" name="page-size" value="A4" checked>
                            A4 (21 × 29.7 سم)
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>
                            <input type="radio" name="page-size" value="A3">
                            A3 (29.7 × 42 سم)
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>
                            <input type="radio" name="orientation" value="portrait" checked>
                            عمودي (Portrait)
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>
                            <input type="radio" name="orientation" value="landscape">
                            أفقي (Landscape)
                        </label>
                    </div>
                </div>

                <div class="setting-group">
                    <h4>إعدادات المحتوى</h4>
                    <div class="setting-item">
                        <label>
                            <input type="checkbox" checked>
                            إظهار الرأس والتذييل
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>
                            <input type="checkbox" checked>
                            إظهار خانات التوقيع
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>
                            <input type="checkbox" checked>
                            طباعة الألوان والخلفيات
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>
                            <input type="checkbox">
                            إضافة رقم الصفحة
                        </label>
                    </div>
                </div>

                <div class="setting-group">
                    <h4>إعدادات الجودة</h4>
                    <div class="setting-item">
                        <label>جودة الطباعة:</label>
                        <select>
                            <option value="high">عالية (600 DPI)</option>
                            <option value="medium" selected>متوسطة (300 DPI)</option>
                            <option value="low">منخفضة (150 DPI)</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label>حجم الخط:</label>
                        <select>
                            <option value="small">صغير</option>
                            <option value="medium" selected>متوسط</option>
                            <option value="large">كبير</option>
                        </select>
                    </div>
                </div>

                <div class="modal-actions">
                    <button class="btn btn-primary" onclick="reportsManager.applyPrintSettings()">
                        <i class="fas fa-check"></i> تطبيق الإعدادات
                    </button>
                    <button class="btn btn-secondary" onclick="closeModal()">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </div>
        `;

        showModal(settingsModal);
    }

    applyPrintSettings() {
        // تطبيق إعدادات الطباعة المحددة
        showNotification('تم تطبيق إعدادات الطباعة بنجاح', 'success');
        closeModal();
    }

    exportReportToPDF() {
        // تحسين تصدير PDF
        const reportContent = document.getElementById('report-content');
        if (!reportContent) {
            showNotification('لا يوجد تقرير للتصدير', 'warning');
            return;
        }

        // إضافة فئة PDF للتنسيق الخاص
        document.body.classList.add('pdf-export');

        // استخدام window.print مع إعدادات PDF
        setTimeout(() => {
            window.print();

            setTimeout(() => {
                document.body.classList.remove('pdf-export');
            }, 1000);
        }, 500);

        showNotification('تم إعداد التقرير للتصدير كـ PDF - اختر "حفظ كـ PDF" من خيارات الطباعة', 'info');
    }

    exportReportToWord() {
        const reportContent = document.getElementById('report-content');
        if (!reportContent) {
            showNotification('لا يوجد تقرير للتصدير', 'warning');
            return;
        }

        try {
            // إنشاء محتوى HTML للتصدير إلى Word
            const wordContent = `
                <!DOCTYPE html>
                <html xmlns:o='urn:schemas-microsoft-com:office:office'
                      xmlns:w='urn:schemas-microsoft-com:office:word'
                      xmlns='http://www.w3.org/TR/REC-html40'>
                <head>
                    <meta charset='utf-8'>
                    <title>تقرير تقنية المعلومات</title>
                    <style>
                        body { font-family: 'Arial', sans-serif; direction: rtl; }
                        table { border-collapse: collapse; width: 100%; }
                        th, td { border: 1px solid #000; padding: 8px; text-align: center; }
                        th { background-color: #f0f0f0; font-weight: bold; }
                        .print-header { text-align: center; margin-bottom: 20px; }
                        .signature-section { margin-top: 30px; }
                    </style>
                </head>
                <body>
                    ${reportContent.innerHTML}
                </body>
                </html>
            `;

            // إنشاء Blob وتحميل الملف
            const blob = new Blob([wordContent], {
                type: 'application/msword'
            });

            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `تقرير_تقنية_المعلومات_${new Date().toISOString().split('T')[0]}.doc`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);

            showNotification('تم تصدير التقرير إلى Word بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في تصدير Word:', error);
            showNotification('خطأ في تصدير التقرير إلى Word', 'error');
        }
    }

    exportReportToExcel() {
        // التحقق من وجود مكتبة XLSX
        if (typeof XLSX === 'undefined') {
            showNotification('مكتبة Excel غير محملة. يرجى إعادة تحميل الصفحة.', 'error');
            return;
        }

        const reportContent = document.getElementById('report-content');
        const tables = reportContent.querySelectorAll('table');

        if (tables.length === 0) {
            showNotification('لا توجد جداول للتصدير', 'warning');
            return;
        }

        try {
            const wb = XLSX.utils.book_new();

            tables.forEach((table, index) => {
                const ws = XLSX.utils.table_to_sheet(table);

                // تحسين تنسيق Excel
                const range = XLSX.utils.decode_range(ws['!ref']);

                // تنسيق الرأس
                for (let C = range.s.c; C <= range.e.c; ++C) {
                    const headerCell = XLSX.utils.encode_cell({ r: 0, c: C });
                    if (ws[headerCell]) {
                        ws[headerCell].s = {
                            font: { bold: true, color: { rgb: "FFFFFF" } },
                            fill: { fgColor: { rgb: "2c3e50" } },
                            alignment: { horizontal: "center", vertical: "center" }
                        };
                    }
                }

                // تنسيق البيانات
                for (let R = range.s.r + 1; R <= range.e.r; ++R) {
                    for (let C = range.s.c; C <= range.e.c; ++C) {
                        const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
                        if (ws[cellAddress]) {
                            ws[cellAddress].s = {
                                alignment: { horizontal: "center", vertical: "center" },
                                border: {
                                    top: { style: "thin" },
                                    bottom: { style: "thin" },
                                    left: { style: "thin" },
                                    right: { style: "thin" }
                                }
                            };
                        }
                    }
                }

                const sheetName = index === 0 ? 'التقرير الرئيسي' : `جدول_${index + 1}`;
                XLSX.utils.book_append_sheet(wb, ws, sheetName);
            });

            const fileName = `تقرير_تقنية_المعلومات_${new Date().toISOString().split('T')[0]}.xlsx`;

            // استخدام Electron Manager إذا كان متاحاً ويعمل في Electron
            if (typeof electronExcelManager !== 'undefined' && electronExcelManager && electronExcelManager.isElectron) {
                try {
                    const result = await electronExcelManager.saveExcelFile(wb, fileName);
                    if (result.success && !result.canceled) {
                        showNotification('تم تصدير التقرير إلى Excel بنجاح مع التنسيق المحسن', 'success');
                    } else if (result.canceled) {
                        showNotification('تم إلغاء العملية', 'info');
                    }
                } catch (error) {
                    console.error('خطأ في Electron Manager:', error);
                    // العودة للطريقة العادية
                    XLSX.writeFile(wb, fileName);
                    showNotification('تم تصدير التقرير إلى Excel بنجاح مع التنسيق المحسن', 'success');
                }
            } else {
                // الطريقة العادية للمتصفح
                XLSX.writeFile(wb, fileName);
                showNotification('تم تصدير التقرير إلى Excel بنجاح مع التنسيق المحسن', 'success');
            }

        } catch (error) {
            console.error('خطأ في تصدير Excel:', error);
            showNotification('خطأ في تصدير التقرير إلى Excel', 'error');
        }
    }
}

// إنشاء مثيل من مدير التقارير
const reportsManager = new ReportsManager();

// دوال مساعدة
function generateSemesterReport() {
    reportsManager.generateSemesterReport();
}

function generateAnnualReport() {
    reportsManager.generateAnnualReport();
}

function showStatistics() {
    reportsManager.showStatistics();
}

function showCharts() {
    reportsManager.showCharts();
}
