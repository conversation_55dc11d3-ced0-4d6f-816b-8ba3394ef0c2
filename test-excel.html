<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار وظائف Excel</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn-success {
            background: #27ae60;
        }
        .btn-success:hover {
            background: #229954;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار وظائف Excel - نظام تقويم تقنية المعلومات</h1>
        
        <div class="test-section">
            <h3>1. اختبار مكتبة XLSX</h3>
            <button class="btn" onclick="testXLSXLibrary()">اختبار تحميل المكتبة</button>
            <div id="xlsx-status"></div>
        </div>

        <div class="test-section">
            <h3>2. اختبار إنشاء ملف Excel</h3>
            <button class="btn" onclick="testCreateExcel()">إنشاء ملف اختبار</button>
            <div id="create-status"></div>
        </div>

        <div class="test-section">
            <h3>3. اختبار قراءة ملف Excel</h3>
            <input type="file" id="test-file" accept=".xlsx,.xls">
            <button class="btn" onclick="testReadExcel()">قراءة الملف</button>
            <div id="read-status"></div>
            <div id="file-content"></div>
        </div>

        <div class="test-section">
            <h3>4. اختبار تصدير بيانات الطلاب</h3>
            <button class="btn btn-success" onclick="testExportStudents()">تصدير بيانات تجريبية</button>
            <div id="export-status"></div>
        </div>

        <div class="test-section">
            <h3>5. اختبار تصدير الدرجات</h3>
            <button class="btn btn-success" onclick="testExportGrades()">تصدير درجات تجريبية</button>
            <div id="grades-status"></div>
        </div>
    </div>

    <script src="js/xlsx.min.js"></script>
    <script>
        function showStatus(elementId, message, type = 'success') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function testXLSXLibrary() {
            if (typeof XLSX !== 'undefined') {
                showStatus('xlsx-status', 'مكتبة XLSX محملة بنجاح! الإصدار: ' + XLSX.version, 'success');
            } else {
                showStatus('xlsx-status', 'خطأ: مكتبة XLSX غير محملة', 'error');
            }
        }

        function testCreateExcel() {
            try {
                if (typeof XLSX === 'undefined') {
                    showStatus('create-status', 'خطأ: مكتبة XLSX غير محملة', 'error');
                    return;
                }

                const testData = [
                    ['الاسم', 'العمر', 'المدينة'],
                    ['أحمد محمد', 25, 'الرياض'],
                    ['فاطمة علي', 23, 'جدة'],
                    ['محمد سالم', 27, 'الدمام']
                ];

                const ws = XLSX.utils.aoa_to_sheet(testData);
                const wb = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(wb, ws, 'اختبار');

                XLSX.writeFile(wb, 'اختبار_Excel.xlsx');
                showStatus('create-status', 'تم إنشاء ملف Excel بنجاح!', 'success');
            } catch (error) {
                showStatus('create-status', 'خطأ في إنشاء الملف: ' + error.message, 'error');
            }
        }

        function testReadExcel() {
            const fileInput = document.getElementById('test-file');
            const file = fileInput.files[0];

            if (!file) {
                showStatus('read-status', 'يرجى اختيار ملف Excel أولاً', 'error');
                return;
            }

            if (typeof XLSX === 'undefined') {
                showStatus('read-status', 'خطأ: مكتبة XLSX غير محملة', 'error');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    
                    const sheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[sheetName];
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                    showStatus('read-status', `تم قراءة الملف بنجاح! عدد الصفوف: ${jsonData.length}`, 'success');
                    
                    let contentHTML = '<h4>محتوى الملف:</h4><table border="1" style="border-collapse: collapse; width: 100%;">';
                    jsonData.forEach((row, index) => {
                        contentHTML += '<tr>';
                        row.forEach(cell => {
                            contentHTML += `<td style="padding: 5px; border: 1px solid #ddd;">${cell || ''}</td>`;
                        });
                        contentHTML += '</tr>';
                        if (index >= 9) { // عرض أول 10 صفوف فقط
                            contentHTML += '<tr><td colspan="' + row.length + '" style="text-align: center; font-style: italic;">... والمزيد</td></tr>';
                            return false;
                        }
                    });
                    contentHTML += '</table>';
                    
                    document.getElementById('file-content').innerHTML = contentHTML;
                } catch (error) {
                    showStatus('read-status', 'خطأ في قراءة الملف: ' + error.message, 'error');
                }
            };
            reader.readAsArrayBuffer(file);
        }

        function testExportStudents() {
            try {
                if (typeof XLSX === 'undefined') {
                    showStatus('export-status', 'خطأ: مكتبة XLSX غير محملة', 'error');
                    return;
                }

                const studentsData = [
                    ['رقم الطالب', 'اسم الطالب', 'الصف', 'الشعبة', 'تاريخ التسجيل'],
                    ['1001', 'أحمد محمد علي', '1', 'أ', '2024-01-15'],
                    ['1002', 'فاطمة أحمد سالم', '1', 'أ', '2024-01-16'],
                    ['1003', 'محمد علي حسن', '2', 'ب', '2024-01-17'],
                    ['1004', 'سارة أحمد محمد', '3', 'ج', '2024-01-18'],
                    ['1005', 'عبدالله سالم أحمد', '4', 'د', '2024-01-19']
                ];

                const ws = XLSX.utils.aoa_to_sheet(studentsData);
                const wb = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(wb, ws, 'قائمة الطلاب');

                XLSX.writeFile(wb, 'قائمة_الطلاب_تجريبية.xlsx');
                showStatus('export-status', 'تم تصدير قائمة الطلاب بنجاح!', 'success');
            } catch (error) {
                showStatus('export-status', 'خطأ في تصدير الطلاب: ' + error.message, 'error');
            }
        }

        function testExportGrades() {
            try {
                if (typeof XLSX === 'undefined') {
                    showStatus('grades-status', 'خطأ: مكتبة XLSX غير محملة', 'error');
                    return;
                }

                const gradesData = [
                    ['رقم الطالب', 'اسم الطالب', 'الاختبار الأول', 'الاختبار الثاني', 'المشروع', 'المشاركة', 'الاختبار النهائي', 'المجموع'],
                    ['1001', 'أحمد محمد علي', 18, 17, 19, 20, 85, 159],
                    ['1002', 'فاطمة أحمد سالم', 20, 19, 20, 18, 90, 167],
                    ['1003', 'محمد علي حسن', 16, 18, 17, 19, 80, 150],
                    ['1004', 'سارة أحمد محمد', 19, 20, 18, 20, 88, 165],
                    ['1005', 'عبدالله سالم أحمد', 17, 16, 19, 17, 82, 151]
                ];

                const ws = XLSX.utils.aoa_to_sheet(gradesData);
                const wb = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(wb, ws, 'درجات الطلاب');

                XLSX.writeFile(wb, 'درجات_الطلاب_تجريبية.xlsx');
                showStatus('grades-status', 'تم تصدير درجات الطلاب بنجاح!', 'success');
            } catch (error) {
                showStatus('grades-status', 'خطأ في تصدير الدرجات: ' + error.message, 'error');
            }
        }

        // اختبار تلقائي عند تحميل الصفحة
        window.onload = function() {
            testXLSXLibrary();
        };
    </script>
</body>
</html>
