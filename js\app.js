// التطبيق الرئيسي - استمارة تقويم تقنية المعلومات
class ITEvaluationApp {
    constructor() {
        this.currentUser = null;
        this.settings = {};
        this.init();
    }

    async init() {
        try {
            // انتظار تحميل قاعدة البيانات
            await this.waitForDatabase();
            
            // تحميل الإعدادات
            await this.loadSettings();
            
            // تهيئة واجهة المستخدم
            this.initUI();
            
            // ربط الأحداث
            this.bindEvents();
            
            // تحديث الإحصائيات
            await this.updateDashboard();
            
            console.log('تم تحميل التطبيق بنجاح');
            
        } catch (error) {
            console.error('خطأ في تهيئة التطبيق:', error);
            showNotification('خطأ في تحميل التطبيق', 'error');
        }
    }

    async waitForDatabase() {
        let attempts = 0;
        const maxAttempts = 50;
        
        while (!dbManager.db && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;
        }
        
        if (!dbManager.db) {
            throw new Error('فشل في الاتصال بقاعدة البيانات');
        }
    }

    async loadSettings() {
        try {
            this.settings = {
                school_name: await dbManager.getSetting('school_name') || 'مدرسة تقنية المعلومات',
                current_academic_year: await dbManager.getSetting('current_academic_year') || '2024-2025',
                app_version: await dbManager.getSetting('app_version') || '1.0.0'
            };
            
            // تحديث واجهة المستخدم بالإعدادات
            this.updateUIWithSettings();
            
        } catch (error) {
            console.error('خطأ في تحميل الإعدادات:', error);
        }
    }

    updateUIWithSettings() {
        // تحديث العام الدراسي الحالي
        const currentYearElement = document.getElementById('current-year');
        if (currentYearElement) {
            currentYearElement.textContent = this.settings.current_academic_year;
        }

        // تحديث اسم المدرسة في الإعدادات
        const schoolNameInput = document.getElementById('school-name');
        if (schoolNameInput) {
            schoolNameInput.value = this.settings.school_name;
        }

        // تحديث العام الدراسي في الإعدادات
        const academicYearInput = document.getElementById('current-academic-year');
        if (academicYearInput) {
            academicYearInput.value = this.settings.current_academic_year;
        }
    }

    initUI() {
        // إظهار القسم الافتراضي (لوحة التحكم)
        showSection('dashboard');
        
        // تحديث الوقت والتاريخ
        updateDateTime();
        
        // إضافة أنماط CSS للإشعارات
        this.addNotificationStyles();
        
        // إضافة أنماط CSS للجداول
        this.addTableStyles();
    }

    bindEvents() {
        // أحداث القائمة الرئيسية
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.dataset.section;
                if (section) {
                    showSection(section);
                    this.onSectionChange(section);
                }
            });
        });

        // أحداث الإعدادات
        this.bindSettingsEvents();
        
        // أحداث لوحة المفاتيح
        this.bindKeyboardEvents();
        
        // أحداث النوافذ
        this.bindWindowEvents();
    }

    bindSettingsEvents() {
        // حفظ الإعدادات
        const schoolNameInput = document.getElementById('school-name');
        const academicYearInput = document.getElementById('current-academic-year');

        if (schoolNameInput) {
            schoolNameInput.addEventListener('change', async (e) => {
                try {
                    await dbManager.setSetting('school_name', e.target.value);
                    this.settings.school_name = e.target.value;
                    showNotification('تم حفظ اسم المدرسة', 'success');
                } catch (error) {
                    console.error('خطأ في حفظ اسم المدرسة:', error);
                    showNotification('خطأ في حفظ اسم المدرسة', 'error');
                }
            });
        }

        if (academicYearInput) {
            academicYearInput.addEventListener('change', async (e) => {
                try {
                    await dbManager.setSetting('current_academic_year', e.target.value);
                    this.settings.current_academic_year = e.target.value;
                    this.updateUIWithSettings();
                    showNotification('تم حفظ العام الدراسي', 'success');
                } catch (error) {
                    console.error('خطأ في حفظ العام الدراسي:', error);
                    showNotification('خطأ في حفظ العام الدراسي', 'error');
                }
            });
        }
    }

    bindKeyboardEvents() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+S للحفظ السريع
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                this.quickSave();
            }
            
            // Escape لإغلاق النوافذ المنبثقة
            if (e.key === 'Escape') {
                closeModal();
            }
            
            // F1 للمساعدة
            if (e.key === 'F1') {
                e.preventDefault();
                this.showHelp();
            }
        });
    }

    bindWindowEvents() {
        // تحذير عند إغلاق النافذة مع وجود تغييرات غير محفوظة
        window.addEventListener('beforeunload', (e) => {
            if (this.hasUnsavedChanges()) {
                e.preventDefault();
                e.returnValue = 'لديك تغييرات غير محفوظة. هل تريد المغادرة؟';
            }
        });

        // تحديث حجم النوافذ
        window.addEventListener('resize', debounce(() => {
            this.handleResize();
        }, 250));
    }

    async onSectionChange(section) {
        try {
            switch (section) {
                case 'dashboard':
                    await this.updateDashboard();
                    break;
                case 'students':
                    if (typeof studentsManager !== 'undefined' && studentsManager) {
                        await studentsManager.loadStudents();
                    }
                    break;
                case 'grades':
                    // لا حاجة لتحميل شيء هنا
                    break;
                case 'final-results':
                    if (typeof finalResultsManager !== 'undefined') {
                        finalResultsManager.clearResults();
                    }
                    break;
                case 'reports':
                    // لا حاجة لتحميل شيء هنا
                    break;
                case 'settings':
                    this.updateUIWithSettings();
                    break;
            }
        } catch (error) {
            console.error(`خطأ في تحميل قسم ${section}:`, error);
            showNotification(`خطأ في تحميل القسم`, 'error');
        }
    }

    async updateDashboard() {
        try {
            // تحديث إحصائيات الطلاب
            const students = await dbManager.getStudents();
            const totalStudentsElement = document.getElementById('total-students');
            if (totalStudentsElement) {
                totalStudentsElement.textContent = students.length;
            }

            // تحديث إحصائيات التقارير
            const grades = await dbManager.getGrades();
            const completedReportsElement = document.getElementById('completed-reports');
            if (completedReportsElement) {
                // حساب عدد التقارير المكتملة (عدد الطلاب الذين لديهم درجات)
                const studentsWithGrades = new Set(grades.map(g => g.student_id));
                completedReportsElement.textContent = studentsWithGrades.size;
            }

        } catch (error) {
            console.error('خطأ في تحديث لوحة التحكم:', error);
        }
    }

    quickSave() {
        // تحديد السياق الحالي وحفظ البيانات المناسبة
        const activeSection = document.querySelector('.content-section.active');
        if (!activeSection) return;

        const sectionId = activeSection.id;
        
        switch (sectionId) {
            case 'grades':
                if (typeof gradesManager !== 'undefined' && gradesManager.saveAllGrades) {
                    gradesManager.saveAllGrades();
                }
                break;
            case 'students':
                showNotification('لا توجد بيانات للحفظ في قسم الطلاب', 'info');
                break;
            default:
                showNotification('لا توجد بيانات للحفظ في هذا القسم', 'info');
        }
    }

    hasUnsavedChanges() {
        // فحص وجود تغييرات غير محفوظة
        const gradeInputs = document.querySelectorAll('.grade-input');
        return gradeInputs.length > 0; // إذا كان هناك جدول درجات مفتوح
    }

    showHelp() {
        const helpContent = `
            <div class="modal-header">
                <h3>المساعدة</h3>
                <button class="btn btn-sm btn-danger" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="help-content">
                <h4>اختصارات لوحة المفاتيح:</h4>
                <ul>
                    <li><strong>Ctrl+S:</strong> حفظ سريع</li>
                    <li><strong>Escape:</strong> إغلاق النوافذ المنبثقة</li>
                    <li><strong>F1:</strong> إظهار المساعدة</li>
                </ul>
                
                <h4>كيفية الاستخدام:</h4>
                <ol>
                    <li>ابدأ بإضافة الطلاب من قسم "إدارة الطلاب"</li>
                    <li>يمكنك استيراد الطلاب من ملف Excel</li>
                    <li>انتقل إلى قسم "إدخال الدرجات" لإدخال درجات الطلاب</li>
                    <li>استخدم قسم "التقارير والإحصائيات" لإنشاء التقارير</li>
                    <li>يمكنك طباعة التقارير أو تصديرها إلى Excel</li>
                </ol>
                
                <h4>نصائح:</h4>
                <ul>
                    <li>احفظ نسخة احتياطية من البيانات بانتظام</li>
                    <li>تأكد من إدخال الدرجات بدقة</li>
                    <li>استخدم الفلاتر للبحث السريع عن الطلاب</li>
                </ul>
            </div>
        `;
        
        showModal(helpContent);
    }

    handleResize() {
        // التعامل مع تغيير حجم النافذة
        const tables = document.querySelectorAll('.data-table');
        tables.forEach(table => {
            // إضافة تمرير أفقي للجداول الكبيرة على الشاشات الصغيرة
            if (window.innerWidth < 768) {
                table.style.fontSize = '0.8rem';
            } else {
                table.style.fontSize = '';
            }
        });
    }

    addNotificationStyles() {
        if (document.getElementById('notification-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'notification-styles';
        styles.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                max-width: 500px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                animation: slideInRight 0.3s ease;
            }
            
            .notification-content {
                display: flex;
                align-items: center;
                gap: 10px;
                padding: 15px;
                color: white;
                font-weight: 500;
            }
            
            .notification-success { background: #27ae60; }
            .notification-error { background: #e74c3c; }
            .notification-warning { background: #f39c12; }
            .notification-info { background: #3498db; }
            
            .notification-close {
                background: none;
                border: none;
                color: white;
                cursor: pointer;
                margin-right: auto;
                padding: 5px;
                border-radius: 3px;
            }
            
            .notification-close:hover {
                background: rgba(255,255,255,0.2);
            }
            
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        
        document.head.appendChild(styles);
    }

    addTableStyles() {
        if (document.getElementById('table-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'table-styles';
        styles.textContent = `
            .grade-excellent { background-color: #d4edda !important; color: #155724; }
            .grade-very-good { background-color: #d1ecf1 !important; color: #0c5460; }
            .grade-good { background-color: #fff3cd !important; color: #856404; }
            .grade-acceptable { background-color: #ffeaa7 !important; color: #6c5ce7; }
            .grade-weak { background-color: #f8d7da !important; color: #721c24; }
            
            .grade-input {
                width: 80px;
                padding: 5px;
                border: 1px solid #ddd;
                border-radius: 4px;
                text-align: center;
                font-family: inherit;
            }
            
            .grade-input:focus {
                outline: none;
                border-color: #3498db;
                box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
            }
            
            .grade-table {
                font-size: 0.9rem;
            }
            
            .grade-table th,
            .grade-table td {
                padding: 8px 4px;
                text-align: center;
                vertical-align: middle;
            }
            
            .total-cell,
            .level-cell,
            .description-cell {
                font-weight: bold;
            }
        `;
        
        document.head.appendChild(styles);
    }

    // وظائف النسخ الاحتياطي
    async backupDatabase() {
        try {
            const data = await dbManager.exportData();
            const filename = `backup_${new Date().toISOString().split('T')[0]}.json`;
            exportToJSON(data, filename);
            showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
        } catch (error) {
            console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
            showNotification('خطأ في إنشاء النسخة الاحتياطية', 'error');
        }
    }

    async resetDatabase() {
        if (!confirm('هل أنت متأكد من إعادة تعيين قاعدة البيانات؟ سيتم حذف جميع البيانات!')) {
            return;
        }

        try {
            // حذف قاعدة البيانات وإعادة إنشائها
            indexedDB.deleteDatabase(dbManager.dbName);
            location.reload(); // إعادة تحميل الصفحة
        } catch (error) {
            console.error('خطأ في إعادة تعيين قاعدة البيانات:', error);
            showNotification('خطأ في إعادة تعيين قاعدة البيانات', 'error');
        }
    }
}

// إنشاء مثيل من التطبيق عند تحميل الصفحة
let app;

document.addEventListener('DOMContentLoaded', () => {
    app = new ITEvaluationApp();
});

// دوال عامة للوصول من HTML
function importExcel() {
    if (studentsManager) {
        studentsManager.showImportModal();
    }
}

// معالجات أحداث Electron
try {
    if (typeof window !== 'undefined' &&
        typeof window.process !== 'undefined' &&
        window.process &&
        window.process.type === 'renderer') {

        const { ipcRenderer } = require('electron');

    // معالج استيراد Excel من القائمة
    ipcRenderer.on('excel-import', async (event, data) => {
        if (data.success) {
            try {
                const workbook = XLSX.read(data.data, { type: 'buffer' });
                const sheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[sheetName];
                const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                if (studentsManager) {
                    await studentsManager.importStudentsFromExcel(jsonData);
                    showNotification('تم استيراد البيانات من القائمة بنجاح', 'success');
                }
            } catch (error) {
                console.error('خطأ في معالجة ملف Excel:', error);
                showNotification('خطأ في معالجة ملف Excel: ' + error.message, 'error');
            }
        } else {
            showNotification('خطأ في قراءة ملف Excel: ' + data.error, 'error');
        }
    });

    // معالج تصدير Excel من القائمة
    ipcRenderer.on('excel-export', async (event, filePath) => {
        if (filePath && studentsManager) {
            try {
                // تصدير البيانات الحالية
                await studentsManager.exportStudentsToExcel();
                showNotification('تم تصدير البيانات من القائمة بنجاح', 'success');
            } catch (error) {
                console.error('خطأ في تصدير البيانات:', error);
                showNotification('خطأ في تصدير البيانات: ' + error.message, 'error');
            }
        }
    });

    } catch (error) {
        console.log('Electron IPC غير متاح في هذه البيئة');
    }
} catch (error) {
    console.log('فحص بيئة Electron فشل:', error);
}

function backupDatabase() {
    if (app) {
        app.backupDatabase();
    }
}

function resetDatabase() {
    if (app) {
        app.resetDatabase();
    }
}

// مكتبة XLSX محملة محلياً في index.html
