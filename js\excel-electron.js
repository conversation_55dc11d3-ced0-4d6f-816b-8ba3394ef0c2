// وظائف Excel المحسنة لـ Electron
// هذا الملف يوفر دعم أفضل لوظائف Excel في تطبيق Electron

class ElectronExcelManager {
    constructor() {
        this.isElectron = this.checkElectronEnvironment();
        this.init();
    }

    checkElectronEnvironment() {
        // التحقق من وجود Electron
        try {
            return typeof window !== 'undefined' &&
                   typeof window.process !== 'undefined' &&
                   window.process &&
                   window.process.type === 'renderer';
        } catch (error) {
            return false;
        }
    }

    init() {
        if (this.isElectron) {
            console.log('تم تشغيل Excel Manager في بيئة Electron');
            this.setupElectronHandlers();
        } else {
            console.log('تم تشغيل Excel Manager في المتصفح العادي');
        }
    }

    setupElectronHandlers() {
        // إعداد معالجات خاصة بـ Electron
        if (window.electronAPI) {
            console.log('Electron API متاح');
        }
    }

    async saveExcelFile(workbook, fileName) {
        if (!workbook) {
            throw new Error('لا يوجد ملف Excel للحفظ');
        }

        try {
            if (this.isElectron && window.electronAPI) {
                // استخدام Electron API
                const buffer = XLSX.write(workbook, { 
                    type: 'buffer', 
                    bookType: 'xlsx' 
                });
                
                const result = await window.electronAPI.excelSaveFile(
                    buffer, 
                    fileName || 'ملف_Excel.xlsx'
                );
                
                if (result.success) {
                    return { success: true, filePath: result.filePath };
                } else if (result.canceled) {
                    return { success: false, canceled: true };
                } else {
                    throw new Error(result.error || 'فشل في حفظ الملف');
                }
            } else {
                // استخدام الطريقة العادية للمتصفح
                XLSX.writeFile(workbook, fileName || 'ملف_Excel.xlsx');
                return { success: true, method: 'browser' };
            }
        } catch (error) {
            console.error('خطأ في حفظ ملف Excel:', error);
            throw error;
        }
    }

    async readExcelFile(filePath) {
        try {
            if (this.isElectron && window.electronAPI) {
                // استخدام Electron API
                const result = await window.electronAPI.excelReadFile(filePath);
                
                if (result.success) {
                    const workbook = XLSX.read(result.data, { type: 'buffer' });
                    return { 
                        success: true, 
                        workbook: workbook,
                        filePath: result.filePath 
                    };
                } else if (result.canceled) {
                    return { success: false, canceled: true };
                } else {
                    throw new Error(result.error || 'فشل في قراءة الملف');
                }
            } else {
                // في المتصفح العادي، نحتاج إلى input file
                return new Promise((resolve, reject) => {
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = '.xlsx,.xls';
                    
                    input.onchange = (e) => {
                        const file = e.target.files[0];
                        if (!file) {
                            resolve({ success: false, canceled: true });
                            return;
                        }

                        const reader = new FileReader();
                        reader.onload = (event) => {
                            try {
                                const data = new Uint8Array(event.target.result);
                                const workbook = XLSX.read(data, { type: 'array' });
                                resolve({ 
                                    success: true, 
                                    workbook: workbook,
                                    fileName: file.name 
                                });
                            } catch (error) {
                                reject(error);
                            }
                        };
                        reader.onerror = () => reject(new Error('فشل في قراءة الملف'));
                        reader.readAsArrayBuffer(file);
                    };
                    
                    input.click();
                });
            }
        } catch (error) {
            console.error('خطأ في قراءة ملف Excel:', error);
            throw error;
        }
    }

    // دالة مساعدة لإنشاء ملف Excel من البيانات
    createWorkbookFromData(data, sheetName = 'البيانات') {
        if (!Array.isArray(data) || data.length === 0) {
            throw new Error('البيانات فارغة أو غير صحيحة');
        }

        const ws = XLSX.utils.aoa_to_sheet(data);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, sheetName);
        
        return wb;
    }

    // دالة مساعدة لاستخراج البيانات من ملف Excel
    extractDataFromWorkbook(workbook, sheetIndex = 0) {
        if (!workbook || !workbook.SheetNames || workbook.SheetNames.length === 0) {
            throw new Error('ملف Excel فارغ أو تالف');
        }

        const sheetName = workbook.SheetNames[sheetIndex];
        const worksheet = workbook.Sheets[sheetName];
        
        return XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    }

    // دالة لتنسيق ملف Excel
    formatWorksheet(worksheet, options = {}) {
        if (!worksheet || !worksheet['!ref']) {
            return worksheet;
        }

        const range = XLSX.utils.decode_range(worksheet['!ref']);
        
        // تنسيق الرأس
        if (options.headerStyle) {
            for (let C = range.s.c; C <= range.e.c; ++C) {
                const headerCell = XLSX.utils.encode_cell({ r: 0, c: C });
                if (worksheet[headerCell]) {
                    worksheet[headerCell].s = options.headerStyle;
                }
            }
        }

        // تعيين عرض الأعمدة
        if (options.columnWidths) {
            worksheet['!cols'] = options.columnWidths;
        }

        return worksheet;
    }

    // دالة لإنشاء تنسيق احترافي
    createProfessionalStyle() {
        return {
            headerStyle: {
                font: { bold: true, color: { rgb: "FFFFFF" } },
                fill: { fgColor: { rgb: "2c3e50" } },
                alignment: { horizontal: "center", vertical: "center" }
            },
            columnWidths: [
                { wch: 15 }, // العمود الأول
                { wch: 25 }, // العمود الثاني
                { wch: 15 }, // العمود الثالث
                { wch: 15 }  // العمود الرابع
            ]
        };
    }

    // دالة للتحقق من صحة البيانات
    validateExcelData(data, requiredColumns = []) {
        if (!Array.isArray(data) || data.length < 2) {
            return { valid: false, error: 'البيانات فارغة أو غير كافية' };
        }

        const headers = data[0];
        const rows = data.slice(1);

        // التحقق من وجود الأعمدة المطلوبة
        for (const col of requiredColumns) {
            if (!headers.includes(col)) {
                return { 
                    valid: false, 
                    error: `العمود المطلوب "${col}" غير موجود` 
                };
            }
        }

        // التحقق من وجود بيانات في الصفوف
        const validRows = rows.filter(row => 
            row && row.some(cell => cell !== null && cell !== undefined && cell !== '')
        );

        if (validRows.length === 0) {
            return { valid: false, error: 'لا توجد بيانات صالحة في الملف' };
        }

        return { 
            valid: true, 
            headers: headers, 
            rows: validRows,
            totalRows: validRows.length 
        };
    }

    // دالة لعرض رسالة حالة
    showStatus(message, type = 'info') {
        if (typeof showNotification === 'function') {
            showNotification(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }
}

// إنشاء مثيل عام
const electronExcelManager = new ElectronExcelManager();

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ElectronExcelManager;
}
