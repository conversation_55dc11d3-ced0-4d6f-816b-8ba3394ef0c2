<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الاختبار النهائي</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn-success {
            background: #27ae60;
        }
        .btn-success:hover {
            background: #229954;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>الاختبار النهائي - نظام تقويم تقنية المعلومات</h1>
    
    <div class="result">
        <h3>اختبار الأزرار الأساسية:</h3>
        <button class="btn" onclick="testBasicButton()">زر أساسي</button>
        <button class="btn" onclick="testNotification()">اختبار الإشعار</button>
        <button class="btn" onclick="testModal()">اختبار النافذة المنبثقة</button>
    </div>

    <div class="result">
        <h3>اختبار وظائف الطلاب:</h3>
        <button class="btn btn-success" onclick="testStudentFunctions()">اختبار دوال الطلاب</button>
        <button class="btn btn-success" onclick="testImportExcel()">اختبار استيراد Excel</button>
        <button class="btn btn-success" onclick="testExportExcel()">اختبار تصدير Excel</button>
    </div>

    <div class="result">
        <h3>اختبار شامل:</h3>
        <button class="btn btn-success" onclick="runCompleteTest()">تشغيل اختبار شامل</button>
    </div>
    
    <div id="test-results" class="result">
        انقر على أي زر لبدء الاختبار...
    </div>

    <!-- تحميل الملفات بالترتيب الصحيح -->
    <script src="js/xlsx.min.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/excel-electron-simple.js"></script>
    <script src="js/database.js"></script>
    <script src="js/students.js"></script>
    <script src="js/grades.js"></script>
    <script src="js/final-results.js"></script>
    <script src="js/reports.js"></script>
    <script src="js/app.js"></script>

    <script>
        function updateResults(message, type = 'info') {
            const results = document.getElementById('test-results');
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : '';
            results.innerHTML = `<div class="${className}">${message}</div>`;
        }

        function testBasicButton() {
            updateResults('✅ الزر الأساسي يعمل بنجاح!', 'success');
        }

        function testNotification() {
            if (typeof showNotification === 'function') {
                showNotification('هذا اختبار للإشعار!', 'success');
                updateResults('✅ دالة showNotification تعمل بنجاح!', 'success');
            } else {
                updateResults('❌ دالة showNotification غير متاحة', 'error');
            }
        }

        function testModal() {
            if (typeof showModal === 'function') {
                showModal('<h3>اختبار النافذة المنبثقة</h3><p>هذا اختبار للنافذة المنبثقة</p><button class="btn" onclick="closeModal()">إغلاق</button>');
                updateResults('✅ دالة showModal تعمل بنجاح!', 'success');
            } else {
                updateResults('❌ دالة showModal غير متاحة', 'error');
            }
        }

        function testStudentFunctions() {
            let results = [];
            
            if (typeof studentsManager !== 'undefined') {
                results.push('✅ studentsManager متاح');
                
                if (typeof studentsManager.showImportModal === 'function') {
                    results.push('✅ showImportModal متاحة');
                } else {
                    results.push('❌ showImportModal غير متاحة');
                }
            } else {
                results.push('❌ studentsManager غير متاح');
            }
            
            if (typeof importExcel === 'function') {
                results.push('✅ importExcel متاحة');
            } else {
                results.push('❌ importExcel غير متاحة');
            }
            
            updateResults(results.join('<br>'), results.some(r => r.includes('❌')) ? 'error' : 'success');
        }

        function testImportExcel() {
            if (typeof importExcel === 'function') {
                updateResults('✅ دالة importExcel متاحة! (لن يتم تشغيلها في الاختبار)', 'success');
            } else {
                updateResults('❌ دالة importExcel غير متاحة', 'error');
            }
        }

        function testExportExcel() {
            if (typeof exportStudentsToExcel === 'function') {
                updateResults('✅ دالة exportStudentsToExcel متاحة! (لن يتم تشغيلها في الاختبار)', 'success');
            } else {
                updateResults('❌ دالة exportStudentsToExcel غير متاحة', 'error');
            }
        }

        function runCompleteTest() {
            updateResults('🔄 جاري تشغيل الاختبار الشامل...', 'info');
            
            setTimeout(() => {
                let allResults = [];
                let errorCount = 0;
                
                // اختبار المكتبات الأساسية
                const libraries = [
                    { name: 'XLSX', check: () => typeof XLSX !== 'undefined' },
                    { name: 'electronExcelManager', check: () => typeof electronExcelManager !== 'undefined' }
                ];
                
                libraries.forEach(lib => {
                    if (lib.check()) {
                        allResults.push(`✅ ${lib.name}`);
                    } else {
                        allResults.push(`❌ ${lib.name}`);
                        errorCount++;
                    }
                });
                
                // اختبار الدوال الأساسية
                const functions = [
                    'showNotification',
                    'showSection',
                    'closeModal',
                    'showModal',
                    'importExcel'
                ];
                
                functions.forEach(func => {
                    if (typeof window[func] === 'function') {
                        allResults.push(`✅ ${func}`);
                    } else {
                        allResults.push(`❌ ${func}`);
                        errorCount++;
                    }
                });
                
                // اختبار المدراء
                const managers = [
                    'studentsManager',
                    'gradesManager',
                    'finalResultsManager',
                    'reportsManager',
                    'dbManager'
                ];
                
                managers.forEach(manager => {
                    if (typeof window[manager] !== 'undefined') {
                        allResults.push(`✅ ${manager}`);
                    } else {
                        allResults.push(`❌ ${manager}`);
                        errorCount++;
                    }
                });
                
                const successRate = ((allResults.length - errorCount) / allResults.length * 100).toFixed(1);
                
                updateResults(
                    `<strong>نتائج الاختبار الشامل:</strong><br>` +
                    `${allResults.join('<br>')}<br><br>` +
                    `<strong>معدل النجاح:</strong> ${successRate}%<br>` +
                    `<strong>الحالة:</strong> ${errorCount === 0 ? '✅ جميع الاختبارات نجحت!' : `⚠️ ${errorCount} اختبار فشل`}`,
                    errorCount === 0 ? 'success' : 'error'
                );
            }, 1000);
        }

        // تسجيل الأخطاء
        window.addEventListener('error', function(e) {
            console.error('خطأ JavaScript:', e.error);
            updateResults(`❌ خطأ JavaScript: ${e.error.message}`, 'error');
        });

        window.addEventListener('unhandledrejection', function(e) {
            console.error('Promise مرفوض:', e.reason);
            updateResults(`❌ Promise مرفوض: ${e.reason}`, 'error');
        });

        // اختبار تلقائي عند التحميل
        window.onload = function() {
            setTimeout(() => {
                updateResults('🚀 تم تحميل الصفحة. جاهز للاختبار!', 'success');
            }, 500);
        };
    </script>
</body>
</html>
