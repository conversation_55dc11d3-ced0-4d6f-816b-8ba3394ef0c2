# وظائف الاستيراد والتصدير - نظام تقويم تقنية المعلومات

## نظرة عامة
تم إضافة وظائف شاملة لاستيراد وتصدير البيانات من وإلى ملفات Excel في نظام تقويم تقنية المعلومات.

## الوظائف المتاحة

### 1. استيراد الطلاب من Excel
- **الموقع**: قسم إدارة الطلاب
- **الزر**: "استيراد من Excel"
- **الوظيفة**: استيراد قائمة الطلاب من ملف Excel

#### متطلبات الملف:
- **الصيغة**: .xlsx أو .xls
- **ترتيب الأعمدة**: رقم الطالب، اسم الطالب، الصف، الشعبة
- **الصف الأول**: يجب أن يحتوي على عناوين الأعمدة
- **رقم الطالب**: يجب أن يكون فريداً (لا يتكرر)
- **الصف**: رقم من 1 إلى 12
- **الشعبة**: حرف واحد (أ، ب، ج، د)

#### الميزات:
- التحقق من صحة البيانات
- تخطي الطلاب الموجودين مسبقاً
- تقرير مفصل عن نتائج الاستيراد
- عرض الأخطاء مع أرقام الصفوف

### 2. تصدير قائمة الطلاب
- **الموقع**: قسم إدارة الطلاب
- **الزر**: "تصدير إلى Excel"
- **الوظيفة**: تصدير قائمة الطلاب الحالية إلى ملف Excel

#### محتوى الملف:
- رقم الطالب
- اسم الطالب
- الصف
- الشعبة
- تاريخ الإضافة

### 3. تحميل نموذج استيراد الطلاب
- **الموقع**: نافذة استيراد الطلاب
- **الزر**: "تحميل نموذج Excel"
- **الوظيفة**: تحميل ملف Excel نموذجي مع أمثلة

#### محتوى النموذج:
- عناوين الأعمدة الصحيحة
- أمثلة توضيحية للبيانات
- تنسيق احترافي

### 4. تصدير جدول الدرجات
- **الموقع**: قسم الدرجات
- **الزر**: "تصدير الدرجات"
- **الوظيفة**: تصدير جدول الدرجات الحالي إلى Excel

#### الميزات:
- تصدير الجدول كما هو معروض
- الاحتفاظ بالتنسيق
- تسمية الملف بالصف والعام الدراسي

### 5. تحميل نموذج الدرجات
- **الموقع**: قسم الدرجات
- **الزر**: "نموذج الدرجات"
- **الوظيفة**: تحميل نموذج فارغ لإدخال الدرجات

#### محتوى النموذج:
- أعمدة الدرجات المختلفة
- أمثلة للطلاب
- تنسيق احترافي مع عرض أعمدة مناسب

### 6. تصدير النتائج النهائية
- **الموقع**: قسم النتائج النهائية
- **الزر**: "تصدير Excel"
- **الوظيفة**: تصدير النتائج النهائية مع الإحصائيات

#### محتوى الملف:
- الترتيب
- بيانات الطالب
- درجات الفصلين
- المجموع الكلي
- المتوسط والنسبة المئوية
- التقدير والمستوى

### 7. تصدير التقارير
- **الموقع**: قسم التقارير
- **الزر**: "تصدير التقرير إلى Excel"
- **الوظيفة**: تصدير التقارير المُنشأة إلى Excel

#### الميزات:
- تصدير جداول متعددة في ملف واحد
- تنسيق احترافي للجداول
- عناوين مناسبة لكل ورقة عمل

## التحسينات المضافة

### 1. التحقق من المكتبة
- فحص وجود مكتبة XLSX قبل التنفيذ
- رسائل خطأ واضحة في حالة عدم التحميل

### 2. معالجة الأخطاء
- معالجة شاملة للأخطاء
- رسائل خطأ مفصلة ومفيدة
- تسجيل الأخطاء في وحدة التحكم

### 3. تحسينات واجهة المستخدم
- نافذة استيراد محسنة مع تعليمات واضحة
- عرض اسم الملف المختار
- ملاحظات مفيدة للمستخدم

### 4. التنسيق الاحترافي
- تنسيق الرؤوس بألوان مميزة
- عرض أعمدة مناسب
- خطوط عريضة للعناوين

## ملف الاختبار
تم إنشاء ملف `test-excel.html` لاختبار جميع وظائف Excel:
- اختبار تحميل المكتبة
- اختبار إنشاء ملفات Excel
- اختبار قراءة ملفات Excel
- اختبار تصدير البيانات

## الاستخدام

### لاستيراد الطلاب:
1. انتقل إلى قسم "إدارة الطلاب"
2. اضغط على "استيراد من Excel"
3. اقرأ التعليمات بعناية
4. اختر ملف Excel المطابق للمتطلبات
5. اضغط على "استيراد البيانات"

### لتصدير البيانات:
1. انتقل إلى القسم المطلوب
2. تأكد من وجود البيانات المطلوب تصديرها
3. اضغط على زر التصدير المناسب
4. سيتم تحميل الملف تلقائياً

## المتطلبات التقنية
- مكتبة XLSX.js (مضمنة في المشروع)
- متصفح حديث يدعم FileReader API
- JavaScript مُفعل

## الملاحظات
- يتم حفظ الملفات في مجلد التحميلات الافتراضي
- أسماء الملفات تتضمن التاريخ لسهولة التنظيم
- جميع النصوص باللغة العربية مع دعم RTL
- التحقق من صحة البيانات قبل الاستيراد
