// الدوال العامة للتطبيق
// هذا الملف يحتوي على جميع الدوال التي يتم استدعاؤها من HTML

// متغيرات عامة للمدراء
let studentsManager = null;
let gradesManager = null;
let finalResultsManager = null;
let reportsManager = null;
let dbManager = null;

// تهيئة المدراء
function initializeManagers() {
    try {
        // تهيئة مدير قاعدة البيانات
        if (typeof DatabaseManager !== 'undefined') {
            dbManager = new DatabaseManager();
            console.log('تم إنشاء dbManager');
        }

        // تهيئة مدير الطلاب
        if (typeof StudentsManager !== 'undefined') {
            studentsManager = new StudentsManager();
            console.log('تم إنشاء studentsManager');
        }

        // تهيئة مدير الدرجات
        if (typeof GradesManager !== 'undefined') {
            gradesManager = new GradesManager();
            console.log('تم إنشاء gradesManager');
        }

        // تهيئة مدير النتائج النهائية
        if (typeof FinalResultsManager !== 'undefined') {
            finalResultsManager = new FinalResultsManager();
            console.log('تم إنشاء finalResultsManager');
        }

        // تهيئة مدير التقارير
        if (typeof ReportsManager !== 'undefined') {
            reportsManager = new ReportsManager();
            console.log('تم إنشاء reportsManager');
        }

    } catch (error) {
        console.error('خطأ في تهيئة المدراء:', error);
    }
}

// دوال الطلاب
function showAddStudentModal() {
    if (studentsManager && typeof studentsManager.showAddStudentModal === 'function') {
        studentsManager.showAddStudentModal();
    } else {
        showNotification('مدير الطلاب غير متاح', 'error');
    }
}

function showImportModal() {
    if (studentsManager && typeof studentsManager.showImportModal === 'function') {
        studentsManager.showImportModal();
    } else {
        showNotification('مدير الطلاب غير متاح', 'error');
    }
}

async function exportStudentsToExcel() {
    if (studentsManager && studentsManager.students) {
        try {
            await exportStudentsData(studentsManager.students);
        } catch (error) {
            console.error('خطأ في تصدير الطلاب:', error);
            showNotification('خطأ في تصدير الطلاب', 'error');
        }
    } else {
        showNotification('لا توجد بيانات طلاب للتصدير', 'warning');
    }
}

function importExcel() {
    showImportModal();
}

// دوال الدرجات
function loadGradeSheet() {
    if (gradesManager && typeof gradesManager.loadGradeSheet === 'function') {
        gradesManager.loadGradeSheet();
    } else {
        showNotification('مدير الدرجات غير متاح', 'error');
    }
}

async function exportGradeSheet() {
    const table = document.querySelector('.grade-table');
    if (table) {
        try {
            await exportGradesData(table);
        } catch (error) {
            console.error('خطأ في تصدير الدرجات:', error);
            showNotification('خطأ في تصدير الدرجات', 'error');
        }
    } else {
        showNotification('لا يوجد جدول درجات للتصدير', 'warning');
    }
}

async function exportGradeTemplate() {
    try {
        await downloadGradeTemplate();
    } catch (error) {
        console.error('خطأ في تحميل نموذج الدرجات:', error);
        showNotification('خطأ في تحميل نموذج الدرجات', 'error');
    }
}

// دوال النتائج النهائية
function calculateFinalResults() {
    if (finalResultsManager && typeof finalResultsManager.calculateResults === 'function') {
        finalResultsManager.calculateResults();
    } else {
        showNotification('مدير النتائج النهائية غير متاح', 'error');
    }
}

async function exportFinalResults() {
    if (finalResultsManager && typeof finalResultsManager.exportToExcel === 'function') {
        try {
            await finalResultsManager.exportToExcel();
        } catch (error) {
            console.error('خطأ في تصدير النتائج:', error);
            showNotification('خطأ في تصدير النتائج', 'error');
        }
    } else {
        showNotification('مدير النتائج النهائية غير متاح', 'error');
    }
}

function printFinalResults() {
    if (finalResultsManager && typeof finalResultsManager.printResults === 'function') {
        finalResultsManager.printResults();
    } else {
        showNotification('مدير النتائج النهائية غير متاح', 'error');
    }
}

// دوال التقارير
function generateSemesterReport() {
    if (reportsManager && typeof reportsManager.generateSemesterReport === 'function') {
        reportsManager.generateSemesterReport();
    } else {
        showNotification('مدير التقارير غير متاح', 'error');
    }
}

function generateAnnualReport() {
    if (reportsManager && typeof reportsManager.generateAnnualReport === 'function') {
        reportsManager.generateAnnualReport();
    } else {
        showNotification('مدير التقارير غير متاح', 'error');
    }
}

function showStatistics() {
    if (reportsManager && typeof reportsManager.showStatistics === 'function') {
        reportsManager.showStatistics();
    } else {
        showNotification('مدير التقارير غير متاح', 'error');
    }
}

function showCharts() {
    if (reportsManager && typeof reportsManager.showCharts === 'function') {
        reportsManager.showCharts();
    } else {
        showNotification('مدير التقارير غير متاح', 'error');
    }
}

async function exportReportToExcel() {
    if (reportsManager && typeof reportsManager.exportReportToExcel === 'function') {
        try {
            await reportsManager.exportReportToExcel();
        } catch (error) {
            console.error('خطأ في تصدير التقرير:', error);
            showNotification('خطأ في تصدير التقرير', 'error');
        }
    } else {
        showNotification('مدير التقارير غير متاح', 'error');
    }
}

// تهيئة المدراء عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        initializeManagers();
        console.log('تم تهيئة جميع المدراء');
    }, 500);
});

// تصدير المتغيرات للنطاق العام
if (typeof window !== 'undefined') {
    window.studentsManager = studentsManager;
    window.gradesManager = gradesManager;
    window.finalResultsManager = finalResultsManager;
    window.reportsManager = reportsManager;
    window.dbManager = dbManager;
}

console.log('تم تحميل الدوال العامة بنجاح');
