# بناء تطبيق استمارة تقويم تقنية المعلومات
# PowerShell Script

param(
    [string]$BuildType = "all"
)

# تعيين الترميز
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   بناء تطبيق استمارة تقويم تقنية المعلومات" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# التحقق من وجود Node.js
try {
    $nodeVersion = node --version
    Write-Host "✅ تم العثور على Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js غير مثبت على النظام" -ForegroundColor Red
    Write-Host ""
    Write-Host "يرجى تثبيت Node.js من الرابط التالي:" -ForegroundColor Yellow
    Write-Host "https://nodejs.org" -ForegroundColor Blue
    Write-Host ""
    Read-Host "اضغط Enter للمتابعة"
    exit 1
}

# التحقق من وجود package.json
if (-not (Test-Path "package.json")) {
    Write-Host "❌ ملف package.json غير موجود" -ForegroundColor Red
    Write-Host "تأكد من أنك في المجلد الصحيح للمشروع" -ForegroundColor Yellow
    Read-Host "اضغط Enter للمتابعة"
    exit 1
}

Write-Host ""
Write-Host "📦 تثبيت التبعيات..." -ForegroundColor Yellow

try {
    npm install
    Write-Host "✅ تم تثبيت التبعيات بنجاح" -ForegroundColor Green
} catch {
    Write-Host "❌ فشل في تثبيت التبعيات" -ForegroundColor Red
    Read-Host "اضغط Enter للمتابعة"
    exit 1
}

Write-Host ""
Write-Host "🧹 تنظيف الملفات السابقة..." -ForegroundColor Yellow

if (Test-Path "dist") {
    Remove-Item -Recurse -Force "dist"
}

if (Test-Path "build\output") {
    Remove-Item -Recurse -Force "build\output"
}

Write-Host ""

# إذا لم يتم تحديد نوع البناء، اسأل المستخدم
if ($BuildType -eq "all") {
    Write-Host "اختر نوع البناء:" -ForegroundColor Cyan
    Write-Host "1. بناء للـ Windows (64-bit)" -ForegroundColor White
    Write-Host "2. بناء للـ Windows (32-bit)" -ForegroundColor White
    Write-Host "3. بناء للـ Windows (كلاهما)" -ForegroundColor White
    Write-Host "4. بناء محمول (Portable)" -ForegroundColor White
    Write-Host "5. بناء جميع الأنواع" -ForegroundColor White
    Write-Host ""
    
    $choice = Read-Host "أدخل اختيارك (1-5)"
    
    switch ($choice) {
        "1" { $BuildType = "win-x64" }
        "2" { $BuildType = "win-x32" }
        "3" { $BuildType = "win-both" }
        "4" { $BuildType = "portable" }
        "5" { $BuildType = "all" }
        default { 
            Write-Host "❌ اختيار غير صحيح" -ForegroundColor Red
            exit 1
        }
    }
}

Write-Host ""

try {
    switch ($BuildType) {
        "win-x64" {
            Write-Host "🔨 بناء التطبيق للـ Windows 64-bit..." -ForegroundColor Yellow
            npm run build-win-x64
        }
        "win-x32" {
            Write-Host "🔨 بناء التطبيق للـ Windows 32-bit..." -ForegroundColor Yellow
            npm run build-win-x32
        }
        "win-both" {
            Write-Host "🔨 بناء التطبيق للـ Windows (كلاهما)..." -ForegroundColor Yellow
            npm run build-win
        }
        "portable" {
            Write-Host "🔨 بناء النسخة المحمولة..." -ForegroundColor Yellow
            npx electron-builder --win --portable
        }
        "all" {
            Write-Host "🔨 بناء جميع الأنواع..." -ForegroundColor Yellow
            npm run build-win
        }
        default {
            Write-Host "❌ نوع بناء غير معروف: $BuildType" -ForegroundColor Red
            exit 1
        }
    }
    
    Write-Host ""
    Write-Host "✅ تم بناء التطبيق بنجاح!" -ForegroundColor Green
    
} catch {
    Write-Host ""
    Write-Host "❌ فشل في بناء التطبيق" -ForegroundColor Red
    Write-Host "تحقق من الأخطاء أعلاه" -ForegroundColor Yellow
    Read-Host "اضغط Enter للمتابعة"
    exit 1
}

Write-Host ""

# عرض الملفات المبنية
if (Test-Path "dist") {
    Write-Host "📁 الملفات المبنية موجودة في مجلد dist:" -ForegroundColor Cyan
    Write-Host ""
    
    $files = Get-ChildItem "dist" -Filter "*.exe" -ErrorAction SilentlyContinue
    $files += Get-ChildItem "dist" -Filter "*.msi" -ErrorAction SilentlyContinue
    $files += Get-ChildItem "dist" -Filter "*.zip" -ErrorAction SilentlyContinue
    
    foreach ($file in $files) {
        $size = [math]::Round($file.Length / 1MB, 2)
        Write-Host "  📄 $($file.Name) ($size MB)" -ForegroundColor White
    }
    
    Write-Host ""
    $open = Read-Host "هل تريد فتح مجلد الملفات المبنية؟ (y/n)"
    if ($open -eq "y" -or $open -eq "Y") {
        Start-Process "dist"
    }
}

Write-Host ""
Write-Host "🎉 انتهى البناء بنجاح!" -ForegroundColor Green
Write-Host ""
Write-Host "ملاحظات:" -ForegroundColor Cyan
Write-Host "- ملف .exe هو المثبت الرئيسي" -ForegroundColor White
Write-Host "- ملف .zip يحتوي على النسخة المحمولة" -ForegroundColor White
Write-Host "- يمكنك توزيع أي من هذه الملفات" -ForegroundColor White
Write-Host ""

Read-Host "اضغط Enter للإنهاء"
