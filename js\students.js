// إدارة الطلاب
class StudentsManager {
    constructor() {
        this.currentStudents = [];
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadStudents();
    }

    bindEvents() {
        // أحداث الفلاتر
        document.getElementById('grade-filter')?.addEventListener('change', () => this.filterStudents());
        document.getElementById('section-filter')?.addEventListener('change', () => this.filterStudents());
        document.getElementById('search-input')?.addEventListener('input', () => this.filterStudents());
    }

    async loadStudents(filters = {}) {
        try {
            this.currentStudents = await dbManager.getStudents(filters);
            this.renderStudentsTable();
            this.updateStudentCount();
        } catch (error) {
            console.error('خطأ في تحميل الطلاب:', error);
            showNotification('خطأ في تحميل بيانات الطلاب', 'error');
        }
    }

    renderStudentsTable() {
        const tbody = document.getElementById('students-tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (this.currentStudents.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center">لا توجد بيانات طلاب</td>
                </tr>
            `;
            return;
        }

        this.currentStudents.forEach(student => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${student.student_number}</td>
                <td>${student.name}</td>
                <td>الصف ${student.grade}</td>
                <td>${student.section}</td>
                <td>${formatDate(student.registration_date)}</td>
                <td>
                    <button class="btn btn-sm btn-info" onclick="studentsManager.editStudent(${student.id})">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="studentsManager.deleteStudent(${student.id})">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    async filterStudents() {
        const filters = {
            grade: document.getElementById('grade-filter')?.value || '',
            section: document.getElementById('section-filter')?.value || '',
            search: document.getElementById('search-input')?.value || ''
        };

        await this.loadStudents(filters);
    }

    updateStudentCount() {
        const totalElement = document.getElementById('total-students');
        if (totalElement) {
            totalElement.textContent = this.currentStudents.length;
        }
    }

    showAddStudentModal() {
        const modalContent = `
            <div class="modal-header">
                <h3>إضافة طالب جديد</h3>
                <button class="btn btn-sm btn-danger" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="add-student-form" class="student-form">
                <div class="form-group">
                    <label for="student-number">رقم الطالب:</label>
                    <input type="number" id="student-number" name="student_number" required>
                </div>
                
                <div class="form-group">
                    <label for="student-name">اسم الطالب:</label>
                    <input type="text" id="student-name" name="name" required>
                </div>
                
                <div class="form-group">
                    <label for="student-grade">الصف:</label>
                    <select id="student-grade" name="grade" required>
                        <option value="">اختر الصف</option>
                        <option value="1">الصف الأول</option>
                        <option value="2">الصف الثاني</option>
                        <option value="3">الصف الثالث</option>
                        <option value="4">الصف الرابع</option>
                        <option value="5">الصف الخامس</option>
                        <option value="6">الصف السادس</option>
                        <option value="7">الصف السابع</option>
                        <option value="8">الصف الثامن</option>
                        <option value="9">الصف التاسع</option>
                        <option value="10">الصف العاشر</option>
                        <option value="11">الصف الحادي عشر</option>
                        <option value="12">الصف الثاني عشر</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="student-section">الشعبة:</label>
                    <select id="student-section" name="section" required>
                        <option value="">اختر الشعبة</option>
                        <option value="أ">أ</option>
                        <option value="ب">ب</option>
                        <option value="ج">ج</option>
                        <option value="د">د</option>
                    </select>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        `;

        showModal(modalContent);
        
        document.getElementById('add-student-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveStudent();
        });
    }

    async saveStudent(studentId = null) {
        const form = document.getElementById('add-student-form');
        const formData = new FormData(form);
        
        const studentData = {
            student_number: formData.get('student_number'),
            name: formData.get('name'),
            grade: formData.get('grade'),
            section: formData.get('section')
        };

        try {
            if (studentId) {
                await dbManager.updateStudent(studentId, studentData);
                showNotification('تم تحديث بيانات الطالب بنجاح', 'success');
            } else {
                await dbManager.addStudent(studentData);
                showNotification('تم إضافة الطالب بنجاح', 'success');
            }
            
            closeModal();
            await this.loadStudents();
        } catch (error) {
            console.error('خطأ في حفظ الطالب:', error);
            showNotification('خطأ في حفظ بيانات الطالب', 'error');
        }
    }

    async editStudent(studentId) {
        try {
            const students = await dbManager.getStudents();
            const student = students.find(s => s.id === studentId);
            
            if (!student) {
                showNotification('الطالب غير موجود', 'error');
                return;
            }

            const modalContent = `
                <div class="modal-header">
                    <h3>تعديل بيانات الطالب</h3>
                    <button class="btn btn-sm btn-danger" onclick="closeModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form id="add-student-form" class="student-form">
                    <div class="form-group">
                        <label for="student-number">رقم الطالب:</label>
                        <input type="number" id="student-number" name="student_number" value="${student.student_number}" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="student-name">اسم الطالب:</label>
                        <input type="text" id="student-name" name="name" value="${student.name}" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="student-grade">الصف:</label>
                        <select id="student-grade" name="grade" required>
                            <option value="">اختر الصف</option>
                            <option value="1" ${student.grade == 1 ? 'selected' : ''}>الصف الأول</option>
                            <option value="2" ${student.grade == 2 ? 'selected' : ''}>الصف الثاني</option>
                            <option value="3" ${student.grade == 3 ? 'selected' : ''}>الصف الثالث</option>
                            <option value="4" ${student.grade == 4 ? 'selected' : ''}>الصف الرابع</option>
                            <option value="5" ${student.grade == 5 ? 'selected' : ''}>الصف الخامس</option>
                            <option value="6" ${student.grade == 6 ? 'selected' : ''}>الصف السادس</option>
                            <option value="7" ${student.grade == 7 ? 'selected' : ''}>الصف السابع</option>
                            <option value="8" ${student.grade == 8 ? 'selected' : ''}>الصف الثامن</option>
                            <option value="9" ${student.grade == 9 ? 'selected' : ''}>الصف التاسع</option>
                            <option value="10" ${student.grade == 10 ? 'selected' : ''}>الصف العاشر</option>
                            <option value="11" ${student.grade == 11 ? 'selected' : ''}>الصف الحادي عشر</option>
                            <option value="12" ${student.grade == 12 ? 'selected' : ''}>الصف الثاني عشر</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="student-section">الشعبة:</label>
                        <select id="student-section" name="section" required>
                            <option value="">اختر الشعبة</option>
                            <option value="أ" ${student.section === 'أ' ? 'selected' : ''}>أ</option>
                            <option value="ب" ${student.section === 'ب' ? 'selected' : ''}>ب</option>
                            <option value="ج" ${student.section === 'ج' ? 'selected' : ''}>ج</option>
                            <option value="د" ${student.section === 'د' ? 'selected' : ''}>د</option>
                        </select>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ التعديلات
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal()">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </form>
            `;

            showModal(modalContent);
            
            document.getElementById('add-student-form').addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveStudent(studentId);
            });

        } catch (error) {
            console.error('خطأ في تحميل بيانات الطالب:', error);
            showNotification('خطأ في تحميل بيانات الطالب', 'error');
        }
    }

    async deleteStudent(studentId) {
        if (!confirm('هل أنت متأكد من حذف هذا الطالب؟ سيتم حذف جميع درجاته أيضاً.')) {
            return;
        }

        try {
            await dbManager.deleteStudent(studentId);
            showNotification('تم حذف الطالب بنجاح', 'success');
            await this.loadStudents();
        } catch (error) {
            console.error('خطأ في حذف الطالب:', error);
            showNotification('خطأ في حذف الطالب', 'error');
        }
    }

    showImportModal() {
        const modalContent = `
            <div class="modal-header">
                <h3>استيراد الطلاب من ملف Excel</h3>
                <button class="btn btn-sm btn-danger" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="import-section">
                <div class="import-instructions">
                    <h4>تعليمات الاستيراد:</h4>
                    <ul>
                        <li><strong>ترتيب الأعمدة:</strong> رقم الطالب، اسم الطالب، الصف، الشعبة</li>
                        <li><strong>صيغة الملف:</strong> Excel (.xlsx أو .xls)</li>
                        <li><strong>الصف الأول:</strong> يجب أن يحتوي على عناوين الأعمدة</li>
                        <li><strong>رقم الطالب:</strong> يجب أن يكون فريداً (لا يتكرر)</li>
                        <li><strong>الصف:</strong> رقم من 1 إلى 12</li>
                        <li><strong>الشعبة:</strong> حرف واحد (أ، ب، ج، د)</li>
                    </ul>
                    <div class="import-note">
                        <i class="fas fa-info-circle"></i>
                        <span>سيتم تخطي الطلاب الموجودين مسبقاً تلقائياً</span>
                    </div>
                </div>

                <div class="file-upload">
                    <input type="file" id="excel-file" accept=".xlsx,.xls" class="file-input">
                    <label for="excel-file" class="file-label">
                        <i class="fas fa-cloud-upload-alt"></i>
                        اختر ملف Excel
                    </label>
                    <div class="file-info" id="file-info" style="display: none;">
                        <i class="fas fa-file-excel"></i>
                        <span id="file-name"></span>
                    </div>
                </div>

                <div class="import-actions">
                    <button class="btn btn-primary" onclick="studentsManager.processExcelFile()">
                        <i class="fas fa-upload"></i> استيراد البيانات
                    </button>
                    <button class="btn btn-info" onclick="studentsManager.downloadTemplate()">
                        <i class="fas fa-download"></i> تحميل نموذج Excel
                    </button>
                </div>
            </div>
        `;

        showModal(modalContent);

        // إضافة مستمع لتغيير الملف
        setTimeout(() => {
            const fileInput = document.getElementById('excel-file');
            const fileInfo = document.getElementById('file-info');
            const fileName = document.getElementById('file-name');

            if (fileInput) {
                fileInput.addEventListener('change', (e) => {
                    if (e.target.files.length > 0) {
                        fileName.textContent = e.target.files[0].name;
                        fileInfo.style.display = 'block';
                    } else {
                        fileInfo.style.display = 'none';
                    }
                });
            }
        }, 100);
    }

    downloadTemplate() {
        // التحقق من وجود مكتبة XLSX
        if (typeof XLSX === 'undefined') {
            showNotification('مكتبة Excel غير محملة. يرجى إعادة تحميل الصفحة.', 'error');
            return;
        }

        try {
            // إنشاء نموذج Excel للاستيراد مع أمثلة متنوعة
            const templateData = [
                ['رقم الطالب', 'اسم الطالب', 'الصف', 'الشعبة'],
                ['1001', 'أحمد محمد علي', '1', 'أ'],
                ['1002', 'فاطمة أحمد سالم', '1', 'أ'],
                ['1003', 'محمد علي حسن', '2', 'ب'],
                ['1004', 'سارة أحمد محمد', '3', 'ج'],
                ['1005', 'عبدالله سالم أحمد', '4', 'د'],
                ['1006', 'نور الدين محمد', '5', 'أ'],
                ['1007', 'ريم عبدالرحمن', '6', 'ب']
            ];

            const ws = XLSX.utils.aoa_to_sheet(templateData);
            const wb = XLSX.utils.book_new();

            // تنسيق الجدول
            const range = XLSX.utils.decode_range(ws['!ref']);

            // تنسيق الرأس
            for (let C = range.s.c; C <= range.e.c; ++C) {
                const headerCell = XLSX.utils.encode_cell({ r: 0, c: C });
                if (ws[headerCell]) {
                    ws[headerCell].s = {
                        font: { bold: true, color: { rgb: "FFFFFF" } },
                        fill: { fgColor: { rgb: "2c3e50" } },
                        alignment: { horizontal: "center", vertical: "center" }
                    };
                }
            }

            // تعيين عرض الأعمدة
            ws['!cols'] = [
                { wch: 15 }, // رقم الطالب
                { wch: 25 }, // اسم الطالب
                { wch: 10 }, // الصف
                { wch: 10 }  // الشعبة
            ];

            XLSX.utils.book_append_sheet(wb, ws, 'الطلاب');

            const fileName = `نموذج_استيراد_الطلاب_${new Date().toISOString().split('T')[0]}.xlsx`;

            // استخدام Electron Manager إذا كان متاحاً
            if (typeof electronExcelManager !== 'undefined') {
                try {
                    const result = await electronExcelManager.saveExcelFile(wb, fileName);
                    if (result.success && !result.canceled) {
                        showNotification('تم حفظ نموذج Excel بنجاح', 'success');
                    } else if (result.canceled) {
                        showNotification('تم إلغاء العملية', 'info');
                    }
                } catch (error) {
                    console.error('خطأ في Electron Manager:', error);
                    // العودة للطريقة العادية
                    XLSX.writeFile(wb, fileName);
                    showNotification('تم تحميل نموذج Excel بنجاح مع أمثلة توضيحية', 'success');
                }
            } else {
                // الطريقة العادية للمتصفح
                XLSX.writeFile(wb, fileName);
                showNotification('تم تحميل نموذج Excel بنجاح مع أمثلة توضيحية', 'success');
            }
        } catch (error) {
            console.error('خطأ في تحميل النموذج:', error);
            showNotification('خطأ في تحميل نموذج Excel', 'error');
        }
    }

    async processExcelFile() {
        // التحقق من وجود مكتبة XLSX
        if (typeof XLSX === 'undefined') {
            showNotification('مكتبة Excel غير محملة. يرجى إعادة تحميل الصفحة.', 'error');
            return;
        }

        try {
            showNotification('جاري معالجة ملف Excel...', 'info');

            // التحقق من وجود Electron Manager
            if (typeof electronExcelManager !== 'undefined') {
                const result = await electronExcelManager.readExcelFile();
                if (result.success && !result.canceled) {
                    const data = electronExcelManager.extractDataFromWorkbook(result.workbook);
                    await this.importStudentsFromExcel(data);
                    closeModal();
                } else if (result.canceled) {
                    showNotification('تم إلغاء العملية', 'info');
                }
            } else {
                // الطريقة العادية للمتصفح
                const fileInput = document.getElementById('excel-file');
                const file = fileInput.files[0];

                if (!file) {
                    showNotification('يرجى اختيار ملف Excel', 'warning');
                    return;
                }

                const data = await this.readExcelFile(file);
                await this.importStudentsFromExcel(data);
                closeModal();
            }
        } catch (error) {
            console.error('خطأ في معالجة ملف Excel:', error);
            showNotification(`خطأ في معالجة ملف Excel: ${error.message}`, 'error');
        }
    }

    readExcelFile(file) {
        return new Promise((resolve, reject) => {
            if (typeof XLSX === 'undefined') {
                reject(new Error('مكتبة XLSX غير محملة'));
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });

                    if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
                        reject(new Error('الملف لا يحتوي على أوراق عمل'));
                        return;
                    }

                    const sheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[sheetName];
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                    if (!jsonData || jsonData.length === 0) {
                        reject(new Error('الملف فارغ أو لا يحتوي على بيانات'));
                        return;
                    }

                    resolve(jsonData);
                } catch (error) {
                    reject(new Error(`خطأ في قراءة الملف: ${error.message}`));
                }
            };
            reader.onerror = () => reject(new Error('خطأ في قراءة الملف'));
            reader.readAsArrayBuffer(file);
        });
    }

    async importStudentsFromExcel(data) {
        if (data.length < 2) {
            showNotification('الملف فارغ أو لا يحتوي على بيانات', 'warning');
            return;
        }

        const headers = data[0];
        const rows = data.slice(1);
        let successCount = 0;
        let errorCount = 0;
        let duplicateCount = 0;
        const errors = [];

        for (let i = 0; i < rows.length; i++) {
            const row = rows[i];
            const rowNumber = i + 2; // +2 لأن الصف الأول هو العناوين والفهرس يبدأ من 0

            // تخطي الصفوف الفارغة
            if (!row || row.length < 4 || !row[0] || !row[1]) {
                continue;
            }

            try {
                // تنظيف البيانات
                const studentData = {
                    student_number: String(row[0]).trim(),
                    name: String(row[1]).trim(),
                    grade: String(row[2]).trim(),
                    section: String(row[3]).trim()
                };

                // التحقق من صحة البيانات
                if (!studentData.student_number || !studentData.name) {
                    errors.push(`الصف ${rowNumber}: رقم الطالب أو الاسم مفقود`);
                    errorCount++;
                    continue;
                }

                // التحقق من وجود الطالب مسبقاً
                const existingStudent = await dbManager.getStudentByNumber(studentData.student_number);
                if (existingStudent) {
                    duplicateCount++;
                    continue;
                }

                await dbManager.addStudent(studentData);
                successCount++;
            } catch (error) {
                console.error(`خطأ في إضافة الطالب في الصف ${rowNumber}:`, error);
                errors.push(`الصف ${rowNumber}: ${error.message}`);
                errorCount++;
            }
        }

        // عرض تقرير مفصل
        let message = `تم استيراد ${successCount} طالب بنجاح.`;
        if (duplicateCount > 0) {
            message += ` تم تخطي ${duplicateCount} طالب (موجود مسبقاً).`;
        }
        if (errorCount > 0) {
            message += ` فشل في استيراد ${errorCount} طالب.`;
        }

        showNotification(message, successCount > 0 ? 'success' : 'warning');

        // عرض الأخطاء إذا وجدت
        if (errors.length > 0 && errors.length <= 5) {
            setTimeout(() => {
                showNotification(`أخطاء الاستيراد:\n${errors.join('\n')}`, 'error');
            }, 2000);
        }

        await this.loadStudents();
    }

    exportStudentsToExcel() {
        // التحقق من وجود مكتبة XLSX
        if (typeof XLSX === 'undefined') {
            showNotification('مكتبة Excel غير محملة. يرجى إعادة تحميل الصفحة.', 'error');
            return;
        }

        if (this.students.length === 0) {
            showNotification('لا توجد بيانات طلاب للتصدير', 'warning');
            return;
        }

        try {
            // إنشاء البيانات للتصدير
            const exportData = [
                ['رقم الطالب', 'اسم الطالب', 'الصف', 'الشعبة', 'تاريخ الإضافة']
            ];

            this.students.forEach(student => {
                exportData.push([
                    student.student_number,
                    student.name,
                    student.grade,
                    student.section,
                    new Date(student.created_at).toLocaleDateString('ar-SA')
                ]);
            });

            // إنشاء ملف Excel
            const ws = XLSX.utils.aoa_to_sheet(exportData);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'قائمة الطلاب');

            // تنسيق الجدول
            const range = XLSX.utils.decode_range(ws['!ref']);
            for (let R = range.s.r; R <= range.e.r; ++R) {
                for (let C = range.s.c; C <= range.e.c; ++C) {
                    const cell_address = XLSX.utils.encode_cell({ c: C, r: R });
                    if (!ws[cell_address]) continue;

                    // تنسيق الرأس
                    if (R === 0) {
                        ws[cell_address].s = {
                            font: { bold: true },
                            fill: { fgColor: { rgb: "2c3e50" } },
                            alignment: { horizontal: "center" }
                        };
                    }
                }
            }

            const fileName = `قائمة_الطلاب_${new Date().toISOString().split('T')[0]}.xlsx`;

            // استخدام Electron Manager إذا كان متاحاً
            if (typeof electronExcelManager !== 'undefined') {
                try {
                    const result = await electronExcelManager.saveExcelFile(wb, fileName);
                    if (result.success && !result.canceled) {
                        showNotification('تم تصدير قائمة الطلاب بنجاح', 'success');
                    } else if (result.canceled) {
                        showNotification('تم إلغاء العملية', 'info');
                    }
                } catch (error) {
                    console.error('خطأ في Electron Manager:', error);
                    // العودة للطريقة العادية
                    XLSX.writeFile(wb, fileName);
                    showNotification('تم تصدير قائمة الطلاب بنجاح', 'success');
                }
            } else {
                // الطريقة العادية للمتصفح
                XLSX.writeFile(wb, fileName);
                showNotification('تم تصدير قائمة الطلاب بنجاح', 'success');
            }

        } catch (error) {
            console.error('خطأ في تصدير قائمة الطلاب:', error);
            showNotification('خطأ في تصدير قائمة الطلاب', 'error');
        }
    }
}

// إنشاء مثيل من مدير الطلاب
const studentsManager = new StudentsManager();

// دوال مساعدة
function showAddStudentModal() {
    studentsManager.showAddStudentModal();
}

function showImportModal() {
    studentsManager.showImportModal();
}

function exportStudentsToExcel() {
    studentsManager.exportStudentsToExcel();
}
