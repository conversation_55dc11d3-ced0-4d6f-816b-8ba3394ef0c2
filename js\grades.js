// إدارة الدرجات
class GradesManager {
    constructor() {
        this.currentGradeLevel = null;
        this.currentStudents = [];
        this.gradeStructures = this.initGradeStructures();
        this.init();
    }

    init() {
        this.bindEvents();
    }

    bindEvents() {
        // أحداث اختيار الصف والشعبة
        document.getElementById('grade-select')?.addEventListener('change', () => this.onGradeChange());
        document.getElementById('section-select')?.addEventListener('change', () => this.onSectionChange());
    }

    initGradeStructures() {
        return {
            // الصفوف 1-4 (المجموع من 50)
            elementary: {
                grades: [1, 2, 3, 4],
                components: [
                    { name: 'oral_work_1', label: 'الأعمال الشفوية (الفترة الأولى)', max: 10 },
                    { name: 'oral_work_2', label: 'الأعمال الشفوية (الفترة الثانية)', max: 10 },
                    { name: 'practical_activities_1', label: 'الأنشطة العملية (الفترة الأولى)', max: 10 },
                    { name: 'practical_activities_2', label: 'الأنشطة العملية (الفترة الثانية)', max: 10 },
                    { name: 'project', label: 'المشروع', max: 10 }
                ],
                total: 50,
                gradeScale: [
                    { min: 47.25, level: '1', description: 'ممتاز' },
                    { min: 44.75, level: '2', description: 'جيد جداً' },
                    { min: 39.75, level: '3', description: 'جيد' },
                    { min: 34.75, level: '4', description: 'مقبول' },
                    { min: 29.75, level: '5', description: 'ضعيف' },
                    { min: 24.75, level: '6', description: 'ضعيف جداً' },
                    { min: 0, level: '7', description: 'راسب' }
                ]
            },
            // الصفوف 5-10 (المجموع من 100)
            intermediate: {
                grades: [5, 6, 7, 8, 9, 10],
                components: [
                    { name: 'oral_work_1', label: 'الأعمال الشفوية (الفترة الأولى)', max: 10 },
                    { name: 'oral_work_2', label: 'الأعمال الشفوية (الفترة الثانية)', max: 10 },
                    { name: 'practical_activities_1', label: 'الأنشطة العملية (الفترة الأولى)', max: 20 },
                    { name: 'practical_activities_2', label: 'الأنشطة العملية (الفترة الثانية)', max: 20 },
                    { name: 'project', label: 'المشروع', max: 20 },
                    { name: 'short_test', label: 'الاختبار القصير', max: 20 }
                ],
                total: 100,
                gradeScale: [
                    { min: 90, level: 'أ', description: 'ممتاز' },
                    { min: 80, level: 'ب', description: 'جيد جداً' },
                    { min: 70, level: 'ج', description: 'جيد' },
                    { min: 60, level: 'د', description: 'مقبول' },
                    { min: 0, level: 'هـ', description: 'راسب' }
                ]
            },
            // الصفوف 11-12 (المجموع من 100)
            secondary: {
                grades: [11, 12],
                components: [
                    { name: 'oral_work_1', label: 'الأعمال الشفوية (الفترة الأولى)', max: 5 },
                    { name: 'oral_work_2', label: 'الأعمال الشفوية (الفترة الثانية)', max: 5 },
                    { name: 'practical_activities_1', label: 'الأنشطة العملية (الفترة الأولى)', max: 20 },
                    { name: 'practical_activities_2', label: 'الأنشطة العملية (الفترة الثانية)', max: 20 },
                    { name: 'project', label: 'المشروع', max: 20 },
                    { name: 'short_test', label: 'الاختبار القصير', max: 20 },
                    { name: 'final_exam', label: 'الاختبار النهائي', max: 30 }
                ],
                total: 100,
                gradeScale: [
                    { min: 90, level: 'أ', description: 'ممتاز' },
                    { min: 80, level: 'ب', description: 'جيد جداً' },
                    { min: 70, level: 'ج', description: 'جيد' },
                    { min: 60, level: 'د', description: 'مقبول' },
                    { min: 0, level: 'هـ', description: 'راسب' }
                ]
            }
        };
    }

    onGradeChange() {
        const gradeSelect = document.getElementById('grade-select');
        const grade = parseInt(gradeSelect.value);
        
        if (grade) {
            this.currentGradeLevel = grade;
            this.updateGradeStructure();
        }
    }

    onSectionChange() {
        // يمكن إضافة منطق إضافي هنا إذا لزم الأمر
    }

    updateGradeStructure() {
        const grade = this.currentGradeLevel;
        let structure;

        if (grade >= 1 && grade <= 4) {
            structure = this.gradeStructures.elementary;
        } else if (grade >= 5 && grade <= 10) {
            structure = this.gradeStructures.intermediate;
        } else if (grade >= 11 && grade <= 12) {
            structure = this.gradeStructures.secondary;
        }

        this.currentStructure = structure;
    }

    async loadGradeSheet() {
        const academicYear = document.getElementById('academic-year').value;
        const semester = document.getElementById('semester').value;
        const grade = document.getElementById('grade-select').value;
        const section = document.getElementById('section-select').value;

        if (!academicYear || !semester || !grade || !section) {
            showNotification('يرجى اختيار جميع الحقول المطلوبة', 'warning');
            return;
        }

        try {
            // تحميل الطلاب
            const students = await dbManager.getStudents({ grade: grade, section: section });
            
            if (students.length === 0) {
                showNotification('لا توجد طلاب في هذا الصف والشعبة', 'warning');
                return;
            }

            this.currentStudents = students;
            this.currentGradeLevel = parseInt(grade);
            this.updateGradeStructure();

            // تحميل الدرجات الموجودة
            const existingGrades = await dbManager.getGrades({
                academic_year: academicYear,
                semester: semester,
                grade_level: grade
            });

            this.renderGradeSheet(existingGrades);

        } catch (error) {
            console.error('خطأ في تحميل جدول الدرجات:', error);
            showNotification('خطأ في تحميل جدول الدرجات', 'error');
        }
    }

    renderGradeSheet(existingGrades = []) {
        const container = document.getElementById('grade-sheet-container');
        if (!container) return;

        const structure = this.currentStructure;
        if (!structure) return;

        // إنشاء جدول الدرجات
        let tableHTML = `
            <div class="grade-sheet-header">
                <h3>جدول الدرجات - الصف ${this.currentGradeLevel}</h3>
                <div class="grade-sheet-info">
                    <span>العام الدراسي: ${document.getElementById('academic-year').value}</span>
                    <span>الفصل: ${document.getElementById('semester').value}</span>
                    <span>الشعبة: ${document.getElementById('section-select').value}</span>
                </div>
            </div>
            <div class="grade-table-container">
                <table class="grade-table">
                    <thead>
                        <tr>
                            <th rowspan="2">الرقم</th>
                            <th rowspan="2">اسم الطالب</th>
        `;

        // إضافة رؤوس الأعمدة للمكونات
        structure.components.forEach(component => {
            tableHTML += `<th>${component.label}<br><small>(${component.max} درجة)</small></th>`;
        });

        tableHTML += `
                            <th rowspan="2">المجموع<br><small>(${structure.total})</small></th>
                            <th rowspan="2">المستوى</th>
                            <th rowspan="2">العبارة الوصفية</th>
                            <th rowspan="2">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        // إضافة صفوف الطلاب
        this.currentStudents.forEach((student, index) => {
            const studentGrades = existingGrades.find(g => g.student_id === student.id) || {};
            
            tableHTML += `
                <tr data-student-id="${student.id}">
                    <td>${student.student_number}</td>
                    <td>${student.name}</td>
            `;

            // إضافة خلايا إدخال الدرجات
            structure.components.forEach(component => {
                const value = studentGrades[component.name] || '';
                tableHTML += `
                    <td>
                        <input type="number" 
                               class="grade-input" 
                               data-component="${component.name}"
                               data-max="${component.max}"
                               value="${value}"
                               min="0" 
                               max="${component.max}"
                               step="0.25">
                    </td>
                `;
            });

            const total = this.calculateTotal(studentGrades, structure);
            const gradeInfo = this.getGradeInfo(total, structure);

            tableHTML += `
                    <td class="total-cell">${total.toFixed(2)}</td>
                    <td class="level-cell">${gradeInfo.level}</td>
                    <td class="description-cell">${gradeInfo.description}</td>
                    <td>
                        <button class="btn btn-sm btn-success" onclick="gradesManager.saveStudentGrades(${student.id})">
                            <i class="fas fa-save"></i> حفظ
                        </button>
                    </td>
                </tr>
            `;
        });

        tableHTML += `
                    </tbody>
                </table>
            </div>
            <div class="grade-sheet-actions">
                <button class="btn btn-primary" onclick="gradesManager.saveAllGrades()">
                    <i class="fas fa-save"></i> حفظ جميع الدرجات
                </button>
                <button class="btn btn-info" onclick="gradesManager.calculateAllTotals()">
                    <i class="fas fa-calculator"></i> حساب المجاميع
                </button>
                <button class="btn btn-success" onclick="gradesManager.exportGradeSheet()">
                    <i class="fas fa-file-excel"></i> تصدير إلى Excel
                </button>
            </div>
        `;

        container.innerHTML = tableHTML;

        // ربط أحداث إدخال الدرجات
        this.bindGradeInputEvents();
    }

    bindGradeInputEvents() {
        const gradeInputs = document.querySelectorAll('.grade-input');
        gradeInputs.forEach(input => {
            input.addEventListener('input', (e) => {
                this.validateGradeInput(e.target);
                this.updateStudentTotal(e.target.closest('tr'));
            });

            input.addEventListener('blur', (e) => {
                this.formatGradeInput(e.target);
            });
        });
    }

    validateGradeInput(input) {
        const max = parseFloat(input.dataset.max);
        const value = parseFloat(input.value);

        if (value > max) {
            input.value = max;
            showNotification(`الدرجة القصوى هي ${max}`, 'warning');
        }

        if (value < 0) {
            input.value = 0;
        }
    }

    formatGradeInput(input) {
        if (input.value) {
            input.value = parseFloat(input.value).toFixed(2);
        }
    }

    updateStudentTotal(row) {
        const inputs = row.querySelectorAll('.grade-input');
        let total = 0;

        inputs.forEach(input => {
            const value = parseFloat(input.value) || 0;
            total += value;
        });

        const totalCell = row.querySelector('.total-cell');
        const levelCell = row.querySelector('.level-cell');
        const descriptionCell = row.querySelector('.description-cell');

        totalCell.textContent = total.toFixed(2);

        const gradeInfo = this.getGradeInfo(total, this.currentStructure);
        levelCell.textContent = gradeInfo.level;
        descriptionCell.textContent = gradeInfo.description;

        // تلوين الخلايا حسب المستوى
        this.applyGradeColors(row, gradeInfo.level);
    }

    calculateTotal(grades, structure) {
        let total = 0;
        structure.components.forEach(component => {
            total += parseFloat(grades[component.name]) || 0;
        });
        return total;
    }

    getGradeInfo(total, structure) {
        for (const grade of structure.gradeScale) {
            if (total >= grade.min) {
                return grade;
            }
        }
        return structure.gradeScale[structure.gradeScale.length - 1];
    }

    applyGradeColors(row, level) {
        // إزالة الألوان السابقة
        row.classList.remove('grade-excellent', 'grade-very-good', 'grade-good', 'grade-acceptable', 'grade-weak');

        // تطبيق اللون المناسب
        if (level === 'أ' || level === '1') {
            row.classList.add('grade-excellent');
        } else if (level === 'ب' || level === '2') {
            row.classList.add('grade-very-good');
        } else if (level === 'ج' || level === '3') {
            row.classList.add('grade-good');
        } else if (level === 'د' || level === '4') {
            row.classList.add('grade-acceptable');
        } else {
            row.classList.add('grade-weak');
        }
    }

    calculateAllTotals() {
        const rows = document.querySelectorAll('#grade-sheet-container tbody tr');
        rows.forEach(row => {
            this.updateStudentTotal(row);
        });
        showNotification('تم حساب جميع المجاميع', 'success');
    }

    async saveStudentGrades(studentId) {
        try {
            const row = document.querySelector(`tr[data-student-id="${studentId}"]`);
            const gradeData = this.collectGradeData(row, studentId);
            
            await dbManager.saveGrades(gradeData);
            showNotification('تم حفظ درجات الطالب بنجاح', 'success');
            
        } catch (error) {
            console.error('خطأ في حفظ الدرجات:', error);
            showNotification('خطأ في حفظ الدرجات', 'error');
        }
    }

    async saveAllGrades() {
        try {
            const rows = document.querySelectorAll('#grade-sheet-container tbody tr');
            let successCount = 0;
            let errorCount = 0;

            for (const row of rows) {
                try {
                    const studentId = parseInt(row.dataset.studentId);
                    const gradeData = this.collectGradeData(row, studentId);
                    await dbManager.saveGrades(gradeData);
                    successCount++;
                } catch (error) {
                    console.error('خطأ في حفظ درجات الطالب:', error);
                    errorCount++;
                }
            }

            showNotification(
                `تم حفظ درجات ${successCount} طالب. فشل في حفظ ${errorCount} طالب.`,
                successCount > 0 ? 'success' : 'error'
            );

        } catch (error) {
            console.error('خطأ في حفظ جميع الدرجات:', error);
            showNotification('خطأ في حفظ الدرجات', 'error');
        }
    }

    collectGradeData(row, studentId) {
        const inputs = row.querySelectorAll('.grade-input');
        const gradeData = {
            student_id: studentId,
            academic_year: document.getElementById('academic-year').value,
            semester: parseInt(document.getElementById('semester').value),
            grade_level: this.currentGradeLevel
        };

        // جمع درجات المكونات
        inputs.forEach(input => {
            const component = input.dataset.component;
            gradeData[component] = parseFloat(input.value) || 0;
        });

        // حساب المجموع والمستوى
        gradeData.total = this.calculateTotal(gradeData, this.currentStructure);
        const gradeInfo = this.getGradeInfo(gradeData.total, this.currentStructure);
        gradeData.level = gradeInfo.level;
        gradeData.descriptive_phrase = gradeInfo.description;

        return gradeData;
    }

    exportGradeSheet() {
        // التحقق من وجود مكتبة XLSX
        if (typeof XLSX === 'undefined') {
            showNotification('مكتبة Excel غير محملة. يرجى إعادة تحميل الصفحة.', 'error');
            return;
        }

        const table = document.querySelector('.grade-table');
        if (!table) {
            showNotification('لا يوجد جدول درجات للتصدير', 'warning');
            return;
        }

        try {
            // تحويل الجدول إلى Excel
            const wb = XLSX.utils.table_to_book(table, { sheet: 'الدرجات' });
            const fileName = `درجات_الصف_${this.currentGradeLevel}_${document.getElementById('academic-year').value}.xlsx`;

            // استخدام Electron Manager إذا كان متاحاً ويعمل في Electron
            if (typeof electronExcelManager !== 'undefined' && electronExcelManager.isElectron) {
                try {
                    const result = await electronExcelManager.saveExcelFile(wb, fileName);
                    if (result.success && !result.canceled) {
                        showNotification('تم تصدير جدول الدرجات بنجاح', 'success');
                    } else if (result.canceled) {
                        showNotification('تم إلغاء العملية', 'info');
                    }
                } catch (error) {
                    console.error('خطأ في Electron Manager:', error);
                    // العودة للطريقة العادية
                    XLSX.writeFile(wb, fileName);
                    showNotification('تم تصدير جدول الدرجات بنجاح', 'success');
                }
            } else {
                // الطريقة العادية للمتصفح
                XLSX.writeFile(wb, fileName);
                showNotification('تم تصدير جدول الدرجات بنجاح', 'success');
            }
        } catch (error) {
            console.error('خطأ في تصدير جدول الدرجات:', error);
            showNotification('خطأ في تصدير جدول الدرجات', 'error');
        }
    }

    exportGradeTemplate() {
        // التحقق من وجود مكتبة XLSX
        if (typeof XLSX === 'undefined') {
            showNotification('مكتبة Excel غير محملة. يرجى إعادة تحميل الصفحة.', 'error');
            return;
        }

        try {
            // إنشاء نموذج درجات فارغ
            const templateData = [
                ['رقم الطالب', 'اسم الطالب', 'الصف', 'الشعبة', 'الاختبار الأول', 'الاختبار الثاني', 'المشروع', 'المشاركة', 'الاختبار النهائي'],
                ['1001', 'أحمد محمد علي', '1', 'أ', '', '', '', '', ''],
                ['1002', 'فاطمة أحمد سالم', '1', 'أ', '', '', '', '', ''],
                ['1003', 'محمد علي حسن', '1', 'أ', '', '', '', '', '']
            ];

            const ws = XLSX.utils.aoa_to_sheet(templateData);
            const wb = XLSX.utils.book_new();

            // تنسيق الجدول
            const range = XLSX.utils.decode_range(ws['!ref']);

            // تنسيق الرأس
            for (let C = range.s.c; C <= range.e.c; ++C) {
                const headerCell = XLSX.utils.encode_cell({ r: 0, c: C });
                if (ws[headerCell]) {
                    ws[headerCell].s = {
                        font: { bold: true, color: { rgb: "FFFFFF" } },
                        fill: { fgColor: { rgb: "2c3e50" } },
                        alignment: { horizontal: "center", vertical: "center" }
                    };
                }
            }

            // تعيين عرض الأعمدة
            ws['!cols'] = [
                { wch: 12 }, // رقم الطالب
                { wch: 20 }, // اسم الطالب
                { wch: 8 },  // الصف
                { wch: 8 },  // الشعبة
                { wch: 12 }, // الاختبار الأول
                { wch: 12 }, // الاختبار الثاني
                { wch: 10 }, // المشروع
                { wch: 10 }, // المشاركة
                { wch: 15 }  // الاختبار النهائي
            ];

            XLSX.utils.book_append_sheet(wb, ws, 'نموذج الدرجات');

            const fileName = `نموذج_درجات_${new Date().toISOString().split('T')[0]}.xlsx`;

            // استخدام Electron Manager إذا كان متاحاً ويعمل في Electron
            if (typeof electronExcelManager !== 'undefined' && electronExcelManager.isElectron) {
                try {
                    const result = await electronExcelManager.saveExcelFile(wb, fileName);
                    if (result.success && !result.canceled) {
                        showNotification('تم حفظ نموذج الدرجات بنجاح', 'success');
                    } else if (result.canceled) {
                        showNotification('تم إلغاء العملية', 'info');
                    }
                } catch (error) {
                    console.error('خطأ في Electron Manager:', error);
                    // العودة للطريقة العادية
                    XLSX.writeFile(wb, fileName);
                    showNotification('تم تحميل نموذج الدرجات بنجاح', 'success');
                }
            } else {
                // الطريقة العادية للمتصفح
                XLSX.writeFile(wb, fileName);
                showNotification('تم تحميل نموذج الدرجات بنجاح', 'success');
            }
        } catch (error) {
            console.error('خطأ في تحميل نموذج الدرجات:', error);
            showNotification('خطأ في تحميل نموذج الدرجات', 'error');
        }
    }
}

// إنشاء مثيل من مدير الدرجات
const gradesManager = new GradesManager();

// دوال مساعدة
function loadGradeSheet() {
    gradesManager.loadGradeSheet();
}
