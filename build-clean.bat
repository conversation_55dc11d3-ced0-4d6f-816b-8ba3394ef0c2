@echo off
title IT Evaluation System - Clean Build
cls

echo.
echo ========================================
echo    IT Evaluation System - Clean Build
echo ========================================
echo.

REM Kill any running processes
echo Stopping any running processes...
taskkill /f /im "IT Evaluation System.exe" 2>nul
taskkill /f /im electron.exe 2>nul
timeout /t 2 /nobreak >nul

REM Force clean everything
echo Performing deep clean...
if exist "dist" (
    echo Removing dist folder...
    rmdir /s /q "dist" 2>nul
    timeout /t 1 /nobreak >nul
    if exist "dist" (
        rd /s /q "dist" 2>nul
        timeout /t 1 /nobreak >nul
    )
)

if exist "node_modules\.cache" rmdir /s /q "node_modules\.cache" 2>nul
if exist "build\output" rmdir /s /q "build\output" 2>nul

echo Clean completed.
echo.

echo Installing/updating dependencies...
call npm install
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Choose build type:
echo 1. Windows 64-bit only (Recommended)
echo 2. Windows 32-bit only
echo 3. Portable version only
echo 4. Exit
echo.

set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" goto build_64
if "%choice%"=="2" goto build_32
if "%choice%"=="3" goto build_portable
if "%choice%"=="4" goto exit_script

echo Invalid choice. Building 64-bit by default...

:build_64
echo.
echo Building Windows 64-bit version...
echo This may take several minutes...
call npx electron-builder --win --x64
goto check_result

:build_32
echo.
echo Building Windows 32-bit version...
echo This may take several minutes...
call npx electron-builder --win --ia32
goto check_result

:build_portable
echo.
echo Building portable version...
echo This may take several minutes...
call npx electron-builder --win portable
goto check_result

:check_result
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ERROR: Build failed
    echo.
    echo Common solutions:
    echo 1. Close any running instances of the app
    echo 2. Run as Administrator
    echo 3. Disable antivirus temporarily
    echo 4. Try building one type at a time
    echo.
    pause
    exit /b 1
)

echo.
echo SUCCESS: Build completed!
echo.

if exist "dist" (
    echo Built files:
    dir /b "dist\*.exe" 2>nul
    echo.
    
    echo Open dist folder? (y/n)
    set /p open="Your choice: "
    if /i "%open%"=="y" (
        start "" "dist"
    )
)

echo.
echo Build completed successfully!
echo You can find the installer in the 'dist' folder.
echo.
pause
exit /b 0

:exit_script
echo.
echo Goodbye!
pause
exit /b 0
