@echo off
title IT Evaluation System Build - نظام تقييم تكنولوجيا المعلومات
chcp 65001 >nul
cls

echo.
echo ========================================
echo    Building IT Evaluation System
echo    بناء نظام تقييم تكنولوجيا المعلومات
echo ========================================
echo.

REM Check for Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Node.js is not installed
    echo خطأ: Node.js غير مثبت
    echo.
    echo Please install Node.js from: https://nodejs.org
    echo يرجى تثبيت Node.js من الرابط أعلاه
    echo.
    echo Press any key to exit...
    pause
    exit /b 1
)

echo SUCCESS: Node.js found
echo تم العثور على Node.js بنجاح
echo.

REM Check for package.json
if not exist "package.json" (
    echo ERROR: package.json not found
    echo خطأ: ملف package.json غير موجود
    echo Make sure you are in the correct project directory
    echo تأكد من أنك في مجلد المشروع الصحيح
    echo.
    echo Press any key to exit...
    pause
    exit /b 1
)

echo Installing dependencies...
echo تثبيت التبعيات...
call npm install
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to install dependencies
    echo خطأ: فشل في تثبيت التبعيات
    echo.
    echo Press any key to exit...
    pause
    exit /b 1
)

echo SUCCESS: Dependencies installed
echo تم تثبيت التبعيات بنجاح
echo.

echo Cleaning previous builds...
echo تنظيف البناءات السابقة...
if exist "dist" rmdir /s /q "dist"
if exist "build\output" rmdir /s /q "build\output"

:menu
cls
echo.
echo ========================================
echo    Choose build type - اختر نوع البناء
echo ========================================
echo.
echo 1. Windows (64-bit) - ويندوز 64 بت
echo 2. Windows (32-bit) - ويندوز 32 بت  
echo 3. Windows (both) - ويندوز كلاهما
echo 4. Portable - نسخة محمولة
echo 5. All types - جميع الأنواع
echo 6. Exit - خروج
echo.

set /p choice="Enter your choice (1-6) - أدخل اختيارك: "

if "%choice%"=="1" goto build_64
if "%choice%"=="2" goto build_32
if "%choice%"=="3" goto build_both
if "%choice%"=="4" goto build_portable
if "%choice%"=="5" goto build_all
if "%choice%"=="6" goto exit_script

echo ERROR: Invalid choice - خطأ: اختيار غير صحيح
echo Please choose a number between 1-6 - يرجى اختيار رقم بين 1-6
echo.
pause
goto menu

:build_64
echo.
echo Building for Windows 64-bit...
echo بناء نسخة ويندوز 64 بت...
call npx electron-builder --win --x64
goto check_result

:build_32
echo.
echo Building for Windows 32-bit...
echo بناء نسخة ويندوز 32 بت...
call npx electron-builder --win --ia32
goto check_result

:build_both
echo.
echo Building for Windows (both)...
echo بناء نسخة ويندوز (كلاهما)...
call npx electron-builder --win --x64 --ia32
goto check_result

:build_portable
echo.
echo Building portable version...
echo بناء النسخة المحمولة...
call npx electron-builder --win --portable
goto check_result

:build_all
echo.
echo Building all types...
echo بناء جميع الأنواع...
call npx electron-builder --win --x64 --ia32 --portable
goto check_result

:check_result
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ERROR: Build failed - خطأ: فشل البناء
    echo Check the errors above - تحقق من الأخطاء أعلاه
    echo.
    echo Press any key to return to menu...
    pause
    goto menu
)

echo.
echo SUCCESS: Build completed! - نجح البناء!
echo.

REM Show built files
if exist "dist" (
    echo Built files in dist folder - الملفات المبنية في مجلد dist:
    echo.
    dir /b "dist\*.exe" 2>nul
    dir /b "dist\*.msi" 2>nul
    dir /b "dist\*.zip" 2>nul
    echo.

    echo Open dist folder? (y/n) - فتح مجلد dist؟
    set /p open="Your choice - اختيارك: "
    if /i "%open%"=="y" (
        start "" "dist"
    )
)

echo.
echo Build completed successfully! - تم البناء بنجاح!
echo.
echo Notes - ملاحظات:
echo - .exe file is the main installer - ملف exe هو المثبت الرئيسي
echo - .zip file contains portable version - ملف zip يحتوي على النسخة المحمولة
echo - You can distribute any of these files - يمكنك توزيع أي من هذه الملفات
echo.

echo What would you like to do next? - ماذا تريد أن تفعل بعد ذلك؟
echo 1. Build another version - بناء نسخة أخرى
echo 2. Exit - خروج
echo.
set /p next="Your choice (1-2) - اختيارك: "

if "%next%"=="1" goto menu
goto exit_script

:exit_script
echo.
echo Thank you for using IT Evaluation System Builder!
echo شكراً لاستخدام أداة بناء نظام تقييم تكنولوجيا المعلومات!
echo.
pause
exit /b 0
