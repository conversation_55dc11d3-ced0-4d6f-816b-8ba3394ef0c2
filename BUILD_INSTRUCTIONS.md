# تعليمات بناء تطبيق سطح المكتب

## 🚀 دليل شامل لتحويل النظام إلى تطبيق exe

### 📋 المتطلبات الأساسية

#### 1. البرامج المطلوبة:
- **Node.js** (الإصدار 16 أو أحدث) - [تحميل من هنا](https://nodejs.org)
- **Git** (اختياري) - [تحميل من هنا](https://git-scm.com)
- **Visual Studio Code** (اختياري) - [تحميل من هنا](https://code.visualstudio.com)

#### 2. متطلبات النظام:
- **Windows**: Windows 7 أو أحدث
- **الذاكرة**: 4 GB RAM كحد أدنى
- **المساحة**: 2 GB مساحة فارغة
- **المعالج**: Intel/AMD 64-bit

### 🛠️ خطوات البناء

#### الطريقة الأولى: استخدام ملف Batch (الأسهل)

1. **افتح Command Prompt كمدير**
2. **انتقل إلى مجلد المشروع**:
   ```cmd
   cd path\to\it-evaluation-system
   ```
3. **شغل ملف البناء**:
   ```cmd
   build.bat
   ```
4. **اتبع التعليمات على الشاشة**

#### الطريقة الثانية: استخدام PowerShell

1. **افتح PowerShell كمدير**
2. **انتقل إلى مجلد المشروع**:
   ```powershell
   cd "path\to\it-evaluation-system"
   ```
3. **شغل ملف البناء**:
   ```powershell
   .\build.ps1
   ```

#### الطريقة الثالثة: الأوامر اليدوية

1. **تثبيت التبعيات**:
   ```cmd
   npm install
   ```

2. **بناء التطبيق**:
   ```cmd
   # للـ Windows 64-bit
   npm run build-win-x64
   
   # للـ Windows 32-bit
   npm run build-win-x32
   
   # للـ Windows (كلاهما)
   npm run build-win
   
   # نسخة محمولة
   npx electron-builder --win --portable
   ```

### 📦 أنواع الملفات المبنية

#### 1. **المثبت (Setup.exe)**
- ملف تثبيت كامل
- يقوم بتثبيت التطبيق في النظام
- ينشئ اختصارات في سطح المكتب وقائمة ابدأ
- يسجل التطبيق في قائمة البرامج المثبتة

#### 2. **النسخة المحمولة (Portable.exe)**
- لا تحتاج تثبيت
- يمكن تشغيلها من أي مكان
- مناسبة للفلاش ديسك
- لا تترك أثراً في النظام

#### 3. **ملف مضغوط (ZIP)**
- يحتوي على جميع ملفات التطبيق
- يمكن استخراجه وتشغيله مباشرة
- مناسب للتوزيع اليدوي

### 🎯 خيارات البناء المتقدمة

#### بناء لمنصات متعددة:
```cmd
# Windows + macOS + Linux
npm run dist-all

# Windows فقط
npm run dist-win
```

#### بناء مع إعدادات مخصصة:
```cmd
# بناء مع تصحيح الأخطاء
npm run build -- --debug

# بناء بدون ضغط
npm run build -- --dir

# بناء مع توقيع رقمي
npm run build -- --publish=never
```

### 📁 هيكل الملفات بعد البناء

```
dist/
├── استمارة تقويم تقنية المعلومات-Setup-1.0.0.exe    # المثبت الرئيسي
├── استمارة تقويم تقنية المعلومات-1.0.0-x64.zip       # النسخة المضغوطة 64-bit
├── استمارة تقويم تقنية المعلومات-1.0.0-ia32.zip      # النسخة المضغوطة 32-bit
├── استمارة تقويم تقنية المعلومات-Portable-1.0.0.exe  # النسخة المحمولة
└── latest.yml                                          # ملف التحديثات
```

### 🔧 حل المشاكل الشائعة

#### مشكلة: "node is not recognized"
**الحل**: تأكد من تثبيت Node.js وإضافته إلى PATH

#### مشكلة: "npm install fails"
**الحل**: 
```cmd
npm cache clean --force
npm install
```

#### مشكلة: "electron-builder fails"
**الحل**:
```cmd
npm install electron-builder --save-dev
npm rebuild
```

#### مشكلة: "Permission denied"
**الحل**: شغل Command Prompt كمدير

#### مشكلة: "Out of memory"
**الحل**:
```cmd
set NODE_OPTIONS=--max-old-space-size=4096
npm run build
```

### 🎨 تخصيص التطبيق

#### تغيير الأيقونة:
1. ضع ملف `icon.ico` في مجلد `assets/`
2. تأكد من أن الأيقونة بحجم 256x256 بكسل
3. أعد البناء

#### تغيير اسم التطبيق:
1. عدل `productName` في `package.json`
2. عدل `title` في `index.html`
3. أعد البناء

#### إضافة شهادة رقمية:
1. احصل على شهادة رقمية
2. أضف إعدادات الشهادة في `package.json`:
   ```json
   "win": {
     "certificateFile": "path/to/certificate.p12",
     "certificatePassword": "password"
   }
   ```

### 📊 إحصائيات البناء

| النوع | الحجم التقريبي | وقت البناء |
|-------|----------------|-------------|
| Setup.exe | ~150 MB | 2-3 دقائق |
| Portable.exe | ~200 MB | 2-3 دقائق |
| ZIP | ~180 MB | 1-2 دقيقة |

### 🚀 التوزيع والنشر

#### للاستخدام الداخلي:
- استخدم ملف Setup.exe للتثبيت العادي
- استخدم Portable.exe للاستخدام المؤقت

#### للتوزيع العام:
- وقع الملفات رقمياً لتجنب تحذيرات Windows
- أنشئ موقع ويب للتحميل
- استخدم خدمات التحديث التلقائي

### 🔄 التحديث التلقائي

التطبيق يدعم التحديث التلقائي عبر:
- فحص التحديثات عند بدء التشغيل
- تحميل التحديثات في الخلفية
- تطبيق التحديثات عند إعادة التشغيل

### 📞 الدعم الفني

في حالة مواجهة مشاكل:
1. تحقق من ملف `build.log`
2. راجع قسم حل المشاكل أعلاه
3. تواصل مع فريق الدعم

---

**🎉 مبروك! الآن لديك تطبيق سطح مكتب احترافي جاهز للتوزيع!**
