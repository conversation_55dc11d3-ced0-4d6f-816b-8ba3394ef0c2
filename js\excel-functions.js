// وظائف Excel منفصلة وآمنة
// هذا الملف يحتوي على جميع وظائف Excel بطريقة آمنة

// فحص بيئة Electron
function isElectronEnv() {
    try {
        return typeof window !== 'undefined' && 
               typeof window.process !== 'undefined' &&
               window.process && 
               window.process.type === 'renderer';
    } catch (error) {
        return false;
    }
}

// حفظ ملف Excel
async function saveExcelFile(workbook, fileName) {
    if (!workbook) {
        throw new Error('لا يوجد ملف Excel للحفظ');
    }

    try {
        if (isElectronEnv() && typeof window.electronAPI !== 'undefined') {
            // استخدام Electron API
            const buffer = XLSX.write(workbook, { 
                type: 'buffer', 
                bookType: 'xlsx' 
            });
            
            const result = await window.electronAPI.excelSaveFile(buffer, fileName);
            
            if (result.success) {
                return { success: true, filePath: result.filePath };
            } else if (result.canceled) {
                return { success: false, canceled: true };
            } else {
                throw new Error(result.error || 'فشل في حفظ الملف');
            }
        } else {
            // استخدام الطريقة العادية للمتصفح
            XLSX.writeFile(workbook, fileName || 'ملف_Excel.xlsx');
            return { success: true, method: 'browser' };
        }
    } catch (error) {
        console.error('خطأ في حفظ ملف Excel:', error);
        // العودة للطريقة العادية في حالة الخطأ
        XLSX.writeFile(workbook, fileName || 'ملف_Excel.xlsx');
        return { success: true, method: 'fallback' };
    }
}

// قراءة ملف Excel
async function readExcelFile(filePath) {
    try {
        if (isElectronEnv() && typeof window.electronAPI !== 'undefined') {
            // استخدام Electron API
            const result = await window.electronAPI.excelReadFile(filePath);
            
            if (result.success) {
                const workbook = XLSX.read(result.data, { type: 'buffer' });
                return { 
                    success: true, 
                    workbook: workbook,
                    filePath: result.filePath 
                };
            } else if (result.canceled) {
                return { success: false, canceled: true };
            } else {
                throw new Error(result.error || 'فشل في قراءة الملف');
            }
        } else {
            // في المتصفح العادي، نحتاج إلى input file
            return new Promise((resolve, reject) => {
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = '.xlsx,.xls';
                
                input.onchange = (e) => {
                    const file = e.target.files[0];
                    if (!file) {
                        resolve({ success: false, canceled: true });
                        return;
                    }

                    const reader = new FileReader();
                    reader.onload = (event) => {
                        try {
                            const data = new Uint8Array(event.target.result);
                            const workbook = XLSX.read(data, { type: 'array' });
                            resolve({ 
                                success: true, 
                                workbook: workbook,
                                fileName: file.name 
                            });
                        } catch (error) {
                            reject(error);
                        }
                    };
                    reader.onerror = () => reject(new Error('فشل في قراءة الملف'));
                    reader.readAsArrayBuffer(file);
                };
                
                input.click();
            });
        }
    } catch (error) {
        console.error('خطأ في قراءة ملف Excel:', error);
        throw error;
    }
}

// استخراج البيانات من ملف Excel
function extractDataFromWorkbook(workbook, sheetIndex = 0) {
    if (!workbook || !workbook.SheetNames || workbook.SheetNames.length === 0) {
        throw new Error('ملف Excel فارغ أو تالف');
    }

    const sheetName = workbook.SheetNames[sheetIndex];
    const worksheet = workbook.Sheets[sheetName];
    
    return XLSX.utils.sheet_to_json(worksheet, { header: 1 });
}

// دوال مساعدة للطلاب
async function downloadStudentTemplate() {
    if (typeof XLSX === 'undefined') {
        showNotification('مكتبة Excel غير محملة. يرجى إعادة تحميل الصفحة.', 'error');
        return;
    }

    try {
        const templateData = [
            ['رقم الطالب', 'اسم الطالب', 'الصف', 'الشعبة'],
            ['1001', 'أحمد محمد علي', '1', 'أ'],
            ['1002', 'فاطمة أحمد سالم', '1', 'أ'],
            ['1003', 'محمد علي حسن', '2', 'ب'],
            ['1004', 'سارة أحمد محمد', '3', 'ج'],
            ['1005', 'عبدالله سالم أحمد', '4', 'د']
        ];

        const ws = XLSX.utils.aoa_to_sheet(templateData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'الطلاب');

        const fileName = `نموذج_استيراد_الطلاب_${new Date().toISOString().split('T')[0]}.xlsx`;
        
        const result = await saveExcelFile(wb, fileName);
        if (result.success) {
            showNotification('تم تحميل نموذج Excel بنجاح', 'success');
        }
    } catch (error) {
        console.error('خطأ في تحميل النموذج:', error);
        showNotification('خطأ في تحميل نموذج Excel', 'error');
    }
}

async function exportStudentsData(students) {
    if (typeof XLSX === 'undefined') {
        showNotification('مكتبة Excel غير محملة. يرجى إعادة تحميل الصفحة.', 'error');
        return;
    }

    if (!students || students.length === 0) {
        showNotification('لا توجد بيانات طلاب للتصدير', 'warning');
        return;
    }

    try {
        const exportData = [
            ['رقم الطالب', 'اسم الطالب', 'الصف', 'الشعبة', 'تاريخ الإضافة']
        ];

        students.forEach(student => {
            exportData.push([
                student.student_number,
                student.name,
                student.grade,
                student.section,
                new Date(student.created_at).toLocaleDateString('ar-SA')
            ]);
        });

        const ws = XLSX.utils.aoa_to_sheet(exportData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'قائمة الطلاب');

        const fileName = `قائمة_الطلاب_${new Date().toISOString().split('T')[0]}.xlsx`;
        
        const result = await saveExcelFile(wb, fileName);
        if (result.success) {
            showNotification('تم تصدير قائمة الطلاب بنجاح', 'success');
        }
    } catch (error) {
        console.error('خطأ في تصدير قائمة الطلاب:', error);
        showNotification('خطأ في تصدير قائمة الطلاب', 'error');
    }
}

// دوال مساعدة للدرجات
async function exportGradesData(table) {
    if (typeof XLSX === 'undefined') {
        showNotification('مكتبة Excel غير محملة. يرجى إعادة تحميل الصفحة.', 'error');
        return;
    }

    if (!table) {
        showNotification('لا يوجد جدول درجات للتصدير', 'warning');
        return;
    }

    try {
        const wb = XLSX.utils.table_to_book(table, { sheet: 'الدرجات' });
        const fileName = `درجات_الصف_${new Date().toISOString().split('T')[0]}.xlsx`;
        
        const result = await saveExcelFile(wb, fileName);
        if (result.success) {
            showNotification('تم تصدير جدول الدرجات بنجاح', 'success');
        }
    } catch (error) {
        console.error('خطأ في تصدير جدول الدرجات:', error);
        showNotification('خطأ في تصدير جدول الدرجات', 'error');
    }
}

async function downloadGradeTemplate() {
    if (typeof XLSX === 'undefined') {
        showNotification('مكتبة Excel غير محملة. يرجى إعادة تحميل الصفحة.', 'error');
        return;
    }

    try {
        const templateData = [
            ['رقم الطالب', 'اسم الطالب', 'الصف', 'الشعبة', 'الاختبار الأول', 'الاختبار الثاني', 'المشروع', 'المشاركة', 'الاختبار النهائي'],
            ['1001', 'أحمد محمد علي', '1', 'أ', '', '', '', '', ''],
            ['1002', 'فاطمة أحمد سالم', '1', 'أ', '', '', '', '', ''],
            ['1003', 'محمد علي حسن', '1', 'أ', '', '', '', '', '']
        ];

        const ws = XLSX.utils.aoa_to_sheet(templateData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'نموذج الدرجات');

        const fileName = `نموذج_درجات_${new Date().toISOString().split('T')[0]}.xlsx`;
        
        const result = await saveExcelFile(wb, fileName);
        if (result.success) {
            showNotification('تم تحميل نموذج الدرجات بنجاح', 'success');
        }
    } catch (error) {
        console.error('خطأ في تحميل نموذج الدرجات:', error);
        showNotification('خطأ في تحميل نموذج الدرجات', 'error');
    }
}

console.log('تم تحميل وظائف Excel بنجاح');
