<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الحل الجديد</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn-success {
            background: #27ae60;
        }
        .btn-success:hover {
            background: #229954;
        }
        .btn-warning {
            background: #f39c12;
        }
        .btn-warning:hover {
            background: #e67e22;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }
        .section h3 {
            margin-top: 0;
            color: #2c3e50;
        }
    </style>
</head>
<body>
    <h1>اختبار الحل الجديد - نظام تقويم تقنية المعلومات</h1>
    
    <div class="section">
        <h3>اختبار الدوال الأساسية:</h3>
        <button class="btn" onclick="testBasicFunctions()">اختبار الدوال الأساسية</button>
        <button class="btn" onclick="testExcelFunctions()">اختبار دوال Excel</button>
        <div id="basic-results" class="result">انقر على الأزرار لاختبار الدوال الأساسية...</div>
    </div>

    <div class="section">
        <h3>اختبار دوال الطلاب:</h3>
        <button class="btn btn-success" onclick="showAddStudentModal()">إضافة طالب</button>
        <button class="btn btn-success" onclick="showImportModal()">استيراد Excel</button>
        <button class="btn btn-success" onclick="exportStudentsToExcel()">تصدير Excel</button>
        <div id="student-results" class="result">اختبر أزرار الطلاب...</div>
    </div>

    <div class="section">
        <h3>اختبار دوال الدرجات:</h3>
        <button class="btn btn-warning" onclick="loadGradeSheet()">تحميل جدول الدرجات</button>
        <button class="btn btn-warning" onclick="exportGradeSheet()">تصدير الدرجات</button>
        <button class="btn btn-warning" onclick="exportGradeTemplate()">نموذج الدرجات</button>
        <div id="grade-results" class="result">اختبر أزرار الدرجات...</div>
    </div>

    <div class="section">
        <h3>اختبار دوال النتائج النهائية:</h3>
        <button class="btn btn-success" onclick="calculateFinalResults()">حساب النتائج</button>
        <button class="btn btn-success" onclick="exportFinalResults()">تصدير النتائج</button>
        <div id="final-results" class="result">اختبر أزرار النتائج النهائية...</div>
    </div>

    <div class="section">
        <h3>اختبار دوال التقارير:</h3>
        <button class="btn" onclick="generateSemesterReport()">تقرير الفصل</button>
        <button class="btn" onclick="generateAnnualReport()">تقرير سنوي</button>
        <button class="btn" onclick="showStatistics()">الإحصائيات</button>
        <button class="btn" onclick="showCharts()">الرسوم البيانية</button>
        <div id="report-results" class="result">اختبر أزرار التقارير...</div>
    </div>

    <div class="section">
        <h3>اختبار شامل:</h3>
        <button class="btn btn-success" onclick="runCompleteTest()">تشغيل اختبار شامل</button>
        <div id="complete-results" class="result">انقر لتشغيل اختبار شامل...</div>
    </div>

    <!-- تحميل الملفات بالترتيب الجديد -->
    <script src="js/xlsx.min.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/excel-functions.js"></script>
    <script src="js/database.js"></script>
    <script src="js/students.js"></script>
    <script src="js/grades.js"></script>
    <script src="js/final-results.js"></script>
    <script src="js/reports.js"></script>
    <script src="js/global-functions.js"></script>
    <script src="js/app.js"></script>

    <script>
        function updateResults(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : '';
            element.innerHTML = `<div class="${className}">${message}</div>`;
        }

        function testBasicFunctions() {
            let results = [];
            
            const functions = [
                'showNotification',
                'showSection',
                'closeModal',
                'showModal'
            ];
            
            functions.forEach(func => {
                if (typeof window[func] === 'function') {
                    results.push(`✅ ${func}`);
                } else {
                    results.push(`❌ ${func}`);
                }
            });
            
            updateResults('basic-results', results.join('<br>'), results.some(r => r.includes('❌')) ? 'error' : 'success');
        }

        function testExcelFunctions() {
            let results = [];
            
            const functions = [
                'saveExcelFile',
                'readExcelFile',
                'extractDataFromWorkbook',
                'downloadStudentTemplate',
                'exportStudentsData'
            ];
            
            functions.forEach(func => {
                if (typeof window[func] === 'function') {
                    results.push(`✅ ${func}`);
                } else {
                    results.push(`❌ ${func}`);
                }
            });
            
            updateResults('basic-results', results.join('<br>'), results.some(r => r.includes('❌')) ? 'error' : 'success');
        }

        function runCompleteTest() {
            updateResults('complete-results', '🔄 جاري تشغيل الاختبار الشامل...', 'info');
            
            setTimeout(() => {
                let allResults = [];
                let errorCount = 0;
                
                // اختبار المكتبات الأساسية
                const libraries = [
                    { name: 'XLSX', check: () => typeof XLSX !== 'undefined' },
                    { name: 'DatabaseManager', check: () => typeof DatabaseManager !== 'undefined' },
                    { name: 'StudentsManager', check: () => typeof StudentsManager !== 'undefined' },
                    { name: 'GradesManager', check: () => typeof GradesManager !== 'undefined' }
                ];
                
                libraries.forEach(lib => {
                    if (lib.check()) {
                        allResults.push(`✅ ${lib.name}`);
                    } else {
                        allResults.push(`❌ ${lib.name}`);
                        errorCount++;
                    }
                });
                
                // اختبار الدوال العامة
                const globalFunctions = [
                    'showAddStudentModal',
                    'showImportModal',
                    'exportStudentsToExcel',
                    'loadGradeSheet',
                    'generateSemesterReport',
                    'calculateFinalResults'
                ];
                
                globalFunctions.forEach(func => {
                    if (typeof window[func] === 'function') {
                        allResults.push(`✅ ${func}`);
                    } else {
                        allResults.push(`❌ ${func}`);
                        errorCount++;
                    }
                });
                
                // اختبار دوال Excel
                const excelFunctions = [
                    'saveExcelFile',
                    'readExcelFile',
                    'downloadStudentTemplate'
                ];
                
                excelFunctions.forEach(func => {
                    if (typeof window[func] === 'function') {
                        allResults.push(`✅ ${func}`);
                    } else {
                        allResults.push(`❌ ${func}`);
                        errorCount++;
                    }
                });
                
                const successRate = ((allResults.length - errorCount) / allResults.length * 100).toFixed(1);
                
                updateResults('complete-results', 
                    `<strong>نتائج الاختبار الشامل:</strong><br>` +
                    `${allResults.join('<br>')}<br><br>` +
                    `<strong>معدل النجاح:</strong> ${successRate}%<br>` +
                    `<strong>الحالة:</strong> ${errorCount === 0 ? '✅ جميع الاختبارات نجحت!' : `⚠️ ${errorCount} اختبار فشل`}`,
                    errorCount === 0 ? 'success' : 'error'
                );
            }, 1000);
        }

        // تسجيل الأخطاء
        window.addEventListener('error', function(e) {
            console.error('خطأ JavaScript:', e.error);
            updateResults('complete-results', `❌ خطأ JavaScript: ${e.error.message}`, 'error');
        });

        window.addEventListener('unhandledrejection', function(e) {
            console.error('Promise مرفوض:', e.reason);
            updateResults('complete-results', `❌ Promise مرفوض: ${e.reason}`, 'error');
        });

        // اختبار تلقائي عند التحميل
        window.onload = function() {
            setTimeout(() => {
                updateResults('complete-results', '🚀 تم تحميل الصفحة. جاهز للاختبار!', 'success');
                
                // اختبار تلقائي
                setTimeout(() => {
                    testBasicFunctions();
                }, 1000);
            }, 500);
        };
    </script>
</body>
</html>
