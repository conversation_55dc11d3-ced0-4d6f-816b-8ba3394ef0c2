<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأزرار</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn-success {
            background: #27ae60;
        }
        .btn-success:hover {
            background: #229954;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار الأزرار - نظام تقويم تقنية المعلومات</h1>
        
        <div class="test-section">
            <h3>اختبار الدوال الأساسية</h3>
            <button class="btn" onclick="testBasicFunctions()">اختبار الدوال الأساسية</button>
            <button class="btn" onclick="testShowNotification()">اختبار الإشعارات</button>
            <button class="btn" onclick="testShowSection()">اختبار عرض الأقسام</button>
            <div id="basic-status"></div>
        </div>

        <div class="test-section">
            <h3>اختبار مدراء النظام</h3>
            <button class="btn" onclick="testManagers()">اختبار المدراء</button>
            <div id="managers-status"></div>
        </div>

        <div class="test-section">
            <h3>اختبار مكتبة XLSX</h3>
            <button class="btn" onclick="testXLSX()">اختبار XLSX</button>
            <div id="xlsx-status"></div>
        </div>

        <div class="test-section">
            <h3>اختبار Excel Manager</h3>
            <button class="btn" onclick="testExcelManager()">اختبار Excel Manager</button>
            <div id="excel-manager-status"></div>
        </div>

        <div class="test-section">
            <h3>اختبار دوال الطلاب</h3>
            <button class="btn btn-success" onclick="testStudentFunctions()">اختبار دوال الطلاب</button>
            <div id="student-status"></div>
        </div>

        <div class="test-section">
            <h3>اختبار شامل</h3>
            <button class="btn btn-success" onclick="runFullTest()">تشغيل اختبار شامل</button>
            <div id="full-status"></div>
        </div>
    </div>

    <!-- تحميل المكتبات والملفات -->
    <script src="js/xlsx.min.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/excel-electron.js"></script>
    <script src="js/database.js"></script>
    <script src="js/students.js"></script>
    <script src="js/grades.js"></script>
    <script src="js/final-results.js"></script>
    <script src="js/reports.js"></script>
    <script src="js/app.js"></script>

    <script>
        function showStatus(elementId, message, type = 'success') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status">${message}</div>`;
        }

        function testBasicFunctions() {
            let results = [];
            
            // اختبار الدوال الأساسية
            if (typeof showNotification === 'function') {
                results.push('✅ showNotification متاحة');
            } else {
                results.push('❌ showNotification غير متاحة');
            }
            
            if (typeof showSection === 'function') {
                results.push('✅ showSection متاحة');
            } else {
                results.push('❌ showSection غير متاحة');
            }
            
            if (typeof closeModal === 'function') {
                results.push('✅ closeModal متاحة');
            } else {
                results.push('❌ closeModal غير متاحة');
            }
            
            showStatus('basic-status', results.join('<br>'));
        }

        function testShowNotification() {
            if (typeof showNotification === 'function') {
                showNotification('هذا اختبار للإشعارات!', 'success');
                showStatus('basic-status', '✅ تم اختبار الإشعارات بنجاح');
            } else {
                showStatus('basic-status', '❌ دالة showNotification غير متاحة');
            }
        }

        function testShowSection() {
            if (typeof showSection === 'function') {
                showStatus('basic-status', '✅ دالة showSection متاحة');
            } else {
                showStatus('basic-status', '❌ دالة showSection غير متاحة');
            }
        }

        function testManagers() {
            let results = [];
            
            if (typeof studentsManager !== 'undefined') {
                results.push('✅ studentsManager متاح');
            } else {
                results.push('❌ studentsManager غير متاح');
            }
            
            if (typeof gradesManager !== 'undefined') {
                results.push('✅ gradesManager متاح');
            } else {
                results.push('❌ gradesManager غير متاح');
            }
            
            if (typeof finalResultsManager !== 'undefined') {
                results.push('✅ finalResultsManager متاح');
            } else {
                results.push('❌ finalResultsManager غير متاح');
            }
            
            if (typeof reportsManager !== 'undefined') {
                results.push('✅ reportsManager متاح');
            } else {
                results.push('❌ reportsManager غير متاح');
            }
            
            showStatus('managers-status', results.join('<br>'));
        }

        function testXLSX() {
            if (typeof XLSX !== 'undefined') {
                showStatus('xlsx-status', `✅ مكتبة XLSX محملة بنجاح! الإصدار: ${XLSX.version || 'غير محدد'}`);
            } else {
                showStatus('xlsx-status', '❌ مكتبة XLSX غير محملة');
            }
        }

        function testExcelManager() {
            let results = [];
            
            if (typeof electronExcelManager !== 'undefined') {
                results.push('✅ electronExcelManager متاح');
                results.push(`البيئة: ${electronExcelManager.isElectron ? 'Electron' : 'متصفح'}`);
            } else {
                results.push('❌ electronExcelManager غير متاح');
            }
            
            showStatus('excel-manager-status', results.join('<br>'));
        }

        function testStudentFunctions() {
            let results = [];
            
            if (typeof importExcel === 'function') {
                results.push('✅ importExcel متاحة');
            } else {
                results.push('❌ importExcel غير متاحة');
            }
            
            if (typeof exportStudentsToExcel === 'function') {
                results.push('✅ exportStudentsToExcel متاحة');
            } else {
                results.push('❌ exportStudentsToExcel غير متاحة');
            }
            
            if (typeof showImportModal === 'function') {
                results.push('✅ showImportModal متاحة');
            } else {
                results.push('❌ showImportModal غير متاحة');
            }
            
            showStatus('student-status', results.join('<br>'));
        }

        async function runFullTest() {
            showStatus('full-status', '🔄 جاري تشغيل الاختبار الشامل...');
            
            let allResults = [];
            
            // اختبار المكتبات الأساسية
            allResults.push(`XLSX: ${typeof XLSX !== 'undefined' ? '✅' : '❌'}`);
            allResults.push(`Excel Manager: ${typeof electronExcelManager !== 'undefined' ? '✅' : '❌'}`);
            
            // اختبار الدوال الأساسية
            allResults.push(`showNotification: ${typeof showNotification === 'function' ? '✅' : '❌'}`);
            allResults.push(`showSection: ${typeof showSection === 'function' ? '✅' : '❌'}`);
            
            // اختبار المدراء
            allResults.push(`studentsManager: ${typeof studentsManager !== 'undefined' ? '✅' : '❌'}`);
            allResults.push(`gradesManager: ${typeof gradesManager !== 'undefined' ? '✅' : '❌'}`);
            
            // اختبار دوال Excel
            allResults.push(`importExcel: ${typeof importExcel === 'function' ? '✅' : '❌'}`);
            
            const passedTests = allResults.filter(r => r.includes('✅')).length;
            const totalTests = allResults.length;
            
            showStatus('full-status', 
                `<strong>نتائج الاختبار الشامل:</strong><br>` +
                `${allResults.join('<br>')}<br><br>` +
                `<strong>النتيجة:</strong> ${passedTests}/${totalTests} اختبار نجح`
            );
        }

        // تشغيل اختبار تلقائي عند تحميل الصفحة
        window.onload = function() {
            setTimeout(() => {
                testBasicFunctions();
                testManagers();
                testXLSX();
                testExcelManager();
            }, 1000);
        };
    </script>
</body>
</html>
