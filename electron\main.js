const { app, BrowserWindow, Menu, dialog, shell, ipcMain, nativeImage } = require('electron');
const path = require('path');
const fs = require('fs');
// const { autoUpdater } = require('electron-updater');

// تعطيل تحذيرات الأمان في بيئة التطوير
process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true';

// تعيين اسم التطبيق
app.setName('استمارة تقويم تقنية المعلومات');

// منع تشغيل عدة نسخ من التطبيق
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
    app.quit();
} else {
    app.on('second-instance', (event, commandLine, workingDirectory) => {
        // إذا حاول المستخدم تشغيل نسخة ثانية، ركز على النافذة الموجودة
        if (mainWindow) {
            if (mainWindow.isMinimized()) mainWindow.restore();
            mainWindow.focus();
        }
    });
}

class ITEvaluationApp {
    constructor() {
        this.mainWindow = null;
        this.isDev = process.argv.includes('--dev');
        this.init();
    }

    init() {
        // التأكد من أن التطبيق جاهز
        app.whenReady().then(() => {
            this.createMainWindow();
            this.createMenu();
            this.setupIPC();
            
            // إنشاء نافذة جديدة عند النقر على أيقونة التطبيق في macOS
            app.on('activate', () => {
                if (BrowserWindow.getAllWindows().length === 0) {
                    this.createMainWindow();
                }
            });
        });

        // إغلاق التطبيق عند إغلاق جميع النوافذ (إلا في macOS)
        app.on('window-all-closed', () => {
            if (process.platform !== 'darwin') {
                app.quit();
            }
        });

        // منع التنقل إلى روابط خارجية
        app.on('web-contents-created', (event, contents) => {
            contents.on('new-window', (navigationEvent, navigationURL) => {
                navigationEvent.preventDefault();
                shell.openExternal(navigationURL);
            });
        });
    }

    createMainWindow() {
        // إنشاء أيقونة التطبيق
        let appIcon;
        try {
            if (process.platform === 'win32') {
                appIcon = path.join(__dirname, '../assets/icon.ico');
            } else if (process.platform === 'darwin') {
                appIcon = path.join(__dirname, '../assets/icon.icns');
            } else {
                appIcon = path.join(__dirname, '../assets/icon.png');
            }

            // التحقق من وجود الأيقونة
            if (!fs.existsSync(appIcon)) {
                appIcon = nativeImage.createFromNamedImage('NSApplicationIcon');
            }
        } catch (error) {
            console.log('تعذر تحميل أيقونة التطبيق:', error);
            appIcon = null;
        }

        // إنشاء النافذة الرئيسية
        this.mainWindow = new BrowserWindow({
            width: 1400,
            height: 900,
            minWidth: 1000,
            minHeight: 700,
            icon: appIcon,
            title: 'استمارة تقويم تقنية المعلومات',
            webPreferences: {
                nodeIntegration: true,
                contextIsolation: false,
                enableRemoteModule: true,
                webSecurity: false,
                allowRunningInsecureContent: true,
                experimentalFeatures: true
            },
            show: false, // لا تظهر النافذة حتى تكون جاهزة
            titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
            backgroundColor: '#f5f6fa',
            center: true,
            resizable: true,
            maximizable: true,
            minimizable: true,
            closable: true,
            autoHideMenuBar: false, // إظهار شريط القوائم
            useContentSize: false,
            thickFrame: true
        });

        // تحميل الملف الرئيسي
        const indexPath = path.join(__dirname, '../index.html');
        this.mainWindow.loadFile(indexPath);

        // إظهار النافذة عند الانتهاء من التحميل
        this.mainWindow.once('ready-to-show', () => {
            this.mainWindow.show();

            // تعظيم النافذة إذا كانت الشاشة كبيرة
            const { screen } = require('electron');
            const primaryDisplay = screen.getPrimaryDisplay();
            const { width, height } = primaryDisplay.workAreaSize;

            if (width >= 1920 && height >= 1080) {
                this.mainWindow.maximize();
            }

            // فتح أدوات المطور في بيئة التطوير
            if (this.isDev) {
                this.mainWindow.webContents.openDevTools();
            }

            // التحقق من التحديثات
            // this.checkForUpdates();
        });

        // التعامل مع إغلاق النافذة
        this.mainWindow.on('close', (event) => {
            // يمكن إضافة تحذير للحفظ هنا
            const choice = dialog.showMessageBoxSync(this.mainWindow, {
                type: 'question',
                buttons: ['نعم', 'لا'],
                defaultId: 0,
                message: 'هل تريد إغلاق التطبيق؟',
                detail: 'تأكد من حفظ جميع البيانات قبل الإغلاق.'
            });

            if (choice === 1) {
                event.preventDefault();
            }
        });

        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });

        // التعامل مع أحداث النافذة
        this.mainWindow.on('maximize', () => {
            this.mainWindow.webContents.send('window-maximized');
        });

        this.mainWindow.on('unmaximize', () => {
            this.mainWindow.webContents.send('window-unmaximized');
        });

        // حفظ حالة النافذة
        this.mainWindow.on('resize', () => {
            this.saveWindowState();
        });

        this.mainWindow.on('move', () => {
            this.saveWindowState();
        });
    }

    saveWindowState() {
        if (!this.mainWindow) return;

        const bounds = this.mainWindow.getBounds();
        const isMaximized = this.mainWindow.isMaximized();

        const windowState = {
            x: bounds.x,
            y: bounds.y,
            width: bounds.width,
            height: bounds.height,
            isMaximized: isMaximized
        };

        // حفظ حالة النافذة في ملف محلي
        try {
            const userDataPath = app.getPath('userData');
            const windowStatePath = path.join(userDataPath, 'window-state.json');
            fs.writeFileSync(windowStatePath, JSON.stringify(windowState, null, 2));
        } catch (error) {
            console.log('فشل في حفظ حالة النافذة:', error);
        }
    }

    loadWindowState() {
        try {
            const userDataPath = app.getPath('userData');
            const windowStatePath = path.join(userDataPath, 'window-state.json');

            if (fs.existsSync(windowStatePath)) {
                const windowState = JSON.parse(fs.readFileSync(windowStatePath, 'utf8'));
                return windowState;
            }
        } catch (error) {
            console.log('فشل في تحميل حالة النافذة:', error);
        }

        return null;
    }

    checkForUpdates() {
        if (this.isDev) return;

        // تم تعطيل التحديث التلقائي مؤقتاً
        console.log('Auto-updater disabled for now');

        /*
        autoUpdater.checkForUpdatesAndNotify();

        autoUpdater.on('update-available', () => {
            dialog.showMessageBox(this.mainWindow, {
                type: 'info',
                title: 'تحديث متاح',
                message: 'يتوفر إصدار جديد من التطبيق. سيتم تحميله في الخلفية.',
                buttons: ['موافق']
            });
        });

        autoUpdater.on('update-downloaded', () => {
            dialog.showMessageBox(this.mainWindow, {
                type: 'info',
                title: 'تحديث جاهز',
                message: 'تم تحميل التحديث. سيتم تطبيقه عند إعادة تشغيل التطبيق.',
                buttons: ['إعادة التشغيل الآن', 'لاحقاً']
            }).then((result) => {
                if (result.response === 0) {
                    autoUpdater.quitAndInstall();
                }
            });
        });
        */
    }

    createMenu() {
        const template = [
            {
                label: 'ملف',
                submenu: [
                    {
                        label: 'جديد',
                        accelerator: 'CmdOrCtrl+N',
                        click: () => {
                            this.mainWindow.webContents.send('menu-new');
                        }
                    },
                    {
                        label: 'فتح',
                        accelerator: 'CmdOrCtrl+O',
                        click: () => {
                            this.openFile();
                        }
                    },
                    {
                        label: 'حفظ',
                        accelerator: 'CmdOrCtrl+S',
                        click: () => {
                            this.mainWindow.webContents.send('menu-save');
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'استيراد من Excel',
                        click: () => {
                            this.importExcel();
                        }
                    },
                    {
                        label: 'تصدير إلى Excel',
                        click: () => {
                            this.exportExcel();
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'نسخة احتياطية',
                        click: () => {
                            this.createBackup();
                        }
                    },
                    {
                        label: 'استعادة من نسخة احتياطية',
                        click: () => {
                            this.restoreBackup();
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'طباعة',
                        accelerator: 'CmdOrCtrl+P',
                        click: () => {
                            this.mainWindow.webContents.print();
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'خروج',
                        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                        click: () => {
                            app.quit();
                        }
                    }
                ]
            },
            {
                label: 'تحرير',
                submenu: [
                    {
                        label: 'تراجع',
                        accelerator: 'CmdOrCtrl+Z',
                        role: 'undo'
                    },
                    {
                        label: 'إعادة',
                        accelerator: 'Shift+CmdOrCtrl+Z',
                        role: 'redo'
                    },
                    { type: 'separator' },
                    {
                        label: 'قص',
                        accelerator: 'CmdOrCtrl+X',
                        role: 'cut'
                    },
                    {
                        label: 'نسخ',
                        accelerator: 'CmdOrCtrl+C',
                        role: 'copy'
                    },
                    {
                        label: 'لصق',
                        accelerator: 'CmdOrCtrl+V',
                        role: 'paste'
                    },
                    {
                        label: 'تحديد الكل',
                        accelerator: 'CmdOrCtrl+A',
                        role: 'selectall'
                    }
                ]
            },
            {
                label: 'عرض',
                submenu: [
                    {
                        label: 'إعادة تحميل',
                        accelerator: 'CmdOrCtrl+R',
                        click: () => {
                            this.mainWindow.reload();
                        }
                    },
                    {
                        label: 'إعادة تحميل قسري',
                        accelerator: 'CmdOrCtrl+Shift+R',
                        click: () => {
                            this.mainWindow.webContents.reloadIgnoringCache();
                        }
                    },
                    {
                        label: 'أدوات المطور',
                        accelerator: 'F12',
                        click: () => {
                            this.mainWindow.webContents.toggleDevTools();
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'تكبير',
                        accelerator: 'CmdOrCtrl+Plus',
                        click: () => {
                            this.mainWindow.webContents.setZoomLevel(
                                this.mainWindow.webContents.getZoomLevel() + 1
                            );
                        }
                    },
                    {
                        label: 'تصغير',
                        accelerator: 'CmdOrCtrl+-',
                        click: () => {
                            this.mainWindow.webContents.setZoomLevel(
                                this.mainWindow.webContents.getZoomLevel() - 1
                            );
                        }
                    },
                    {
                        label: 'الحجم الطبيعي',
                        accelerator: 'CmdOrCtrl+0',
                        click: () => {
                            this.mainWindow.webContents.setZoomLevel(0);
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'ملء الشاشة',
                        accelerator: 'F11',
                        click: () => {
                            this.mainWindow.setFullScreen(!this.mainWindow.isFullScreen());
                        }
                    }
                ]
            },
            {
                label: 'مساعدة',
                submenu: [
                    {
                        label: 'حول التطبيق',
                        click: () => {
                            this.showAbout();
                        }
                    },
                    {
                        label: 'التحقق من التحديثات',
                        click: () => {
                            this.checkForUpdates();
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'إعادة تعيين النافذة',
                        click: () => {
                            this.resetWindow();
                        }
                    },
                    {
                        label: 'دليل المستخدم',
                        click: () => {
                            this.showUserGuide();
                        }
                    },
                    {
                        label: 'الدعم الفني',
                        click: () => {
                            shell.openExternal('mailto:<EMAIL>');
                        }
                    }
                ]
            }
        ];

        // إضافة قائمة خاصة بـ macOS
        if (process.platform === 'darwin') {
            template.unshift({
                label: app.getName(),
                submenu: [
                    {
                        label: 'حول ' + app.getName(),
                        role: 'about'
                    },
                    { type: 'separator' },
                    {
                        label: 'الخدمات',
                        role: 'services',
                        submenu: []
                    },
                    { type: 'separator' },
                    {
                        label: 'إخفاء ' + app.getName(),
                        accelerator: 'Command+H',
                        role: 'hide'
                    },
                    {
                        label: 'إخفاء الآخرين',
                        accelerator: 'Command+Shift+H',
                        role: 'hideothers'
                    },
                    {
                        label: 'إظهار الكل',
                        role: 'unhide'
                    },
                    { type: 'separator' },
                    {
                        label: 'خروج',
                        accelerator: 'Command+Q',
                        click: () => {
                            app.quit();
                        }
                    }
                ]
            });
        }

        const menu = Menu.buildFromTemplate(template);
        Menu.setApplicationMenu(menu);
    }

    setupIPC() {
        // التعامل مع رسائل IPC من العملية المرسلة
        ipcMain.handle('show-save-dialog', async (event, options) => {
            const result = await dialog.showSaveDialog(this.mainWindow, options);
            return result;
        });

        ipcMain.handle('show-open-dialog', async (event, options) => {
            const result = await dialog.showOpenDialog(this.mainWindow, options);
            return result;
        });

        ipcMain.handle('show-message-box', async (event, options) => {
            const result = await dialog.showMessageBox(this.mainWindow, options);
            return result;
        });

        // وظائف Excel المحسنة
        ipcMain.handle('excel-save-file', async (event, data, fileName) => {
            try {
                const result = await dialog.showSaveDialog(this.mainWindow, {
                    title: 'حفظ ملف Excel',
                    defaultPath: fileName,
                    filters: [
                        { name: 'ملفات Excel', extensions: ['xlsx'] },
                        { name: 'جميع الملفات', extensions: ['*'] }
                    ]
                });

                if (!result.canceled) {
                    // كتابة البيانات إلى الملف
                    fs.writeFileSync(result.filePath, data);
                    return { success: true, filePath: result.filePath };
                }
                return { success: false, canceled: true };
            } catch (error) {
                console.error('خطأ في حفظ ملف Excel:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('excel-read-file', async (event, filePath) => {
            try {
                if (!filePath) {
                    const result = await dialog.showOpenDialog(this.mainWindow, {
                        title: 'فتح ملف Excel',
                        filters: [
                            { name: 'ملفات Excel', extensions: ['xlsx', 'xls'] },
                            { name: 'جميع الملفات', extensions: ['*'] }
                        ],
                        properties: ['openFile']
                    });

                    if (result.canceled || result.filePaths.length === 0) {
                        return { success: false, canceled: true };
                    }
                    filePath = result.filePaths[0];
                }

                const data = fs.readFileSync(filePath);
                return { success: true, data: data, filePath: filePath };
            } catch (error) {
                console.error('خطأ في قراءة ملف Excel:', error);
                return { success: false, error: error.message };
            }
        });

        // وظائف النافذة
        ipcMain.handle('minimize-window', () => {
            if (this.mainWindow) {
                this.mainWindow.minimize();
            }
        });

        ipcMain.handle('maximize-window', () => {
            if (this.mainWindow) {
                if (this.mainWindow.isMaximized()) {
                    this.mainWindow.unmaximize();
                } else {
                    this.mainWindow.maximize();
                }
            }
        });

        ipcMain.handle('close-window', () => {
            if (this.mainWindow) {
                this.mainWindow.close();
            }
        });

        // وظائف إضافية
        ipcMain.handle('get-app-version', () => {
            return app.getVersion();
        });

        ipcMain.handle('get-app-path', () => {
            return app.getAppPath();
        });

        ipcMain.handle('open-external', (event, url) => {
            shell.openExternal(url);
        });
    }

    async openFile() {
        const result = await dialog.showOpenDialog(this.mainWindow, {
            title: 'فتح ملف',
            filters: [
                { name: 'ملفات JSON', extensions: ['json'] },
                { name: 'جميع الملفات', extensions: ['*'] }
            ],
            properties: ['openFile']
        });

        if (!result.canceled && result.filePaths.length > 0) {
            this.mainWindow.webContents.send('file-opened', result.filePaths[0]);
        }
    }

    async importExcel() {
        const result = await dialog.showOpenDialog(this.mainWindow, {
            title: 'استيراد من Excel',
            filters: [
                { name: 'ملفات Excel', extensions: ['xlsx', 'xls'] },
                { name: 'جميع الملفات', extensions: ['*'] }
            ],
            properties: ['openFile']
        });

        if (!result.canceled && result.filePaths.length > 0) {
            // قراءة الملف وإرساله إلى العملية المرسلة
            try {
                const data = fs.readFileSync(result.filePaths[0]);
                this.mainWindow.webContents.send('excel-import', {
                    success: true,
                    data: data,
                    filePath: result.filePaths[0]
                });
            } catch (error) {
                this.mainWindow.webContents.send('excel-import', {
                    success: false,
                    error: error.message
                });
            }
        }
    }

    async exportExcel() {
        const result = await dialog.showSaveDialog(this.mainWindow, {
            title: 'تصدير إلى Excel',
            defaultPath: `تقرير_${new Date().toISOString().split('T')[0]}.xlsx`,
            filters: [
                { name: 'ملفات Excel', extensions: ['xlsx'] }
            ]
        });

        if (!result.canceled) {
            this.mainWindow.webContents.send('excel-export', result.filePath);
        }
    }

    async createBackup() {
        const result = await dialog.showSaveDialog(this.mainWindow, {
            title: 'إنشاء نسخة احتياطية',
            defaultPath: `backup_${new Date().toISOString().split('T')[0]}.json`,
            filters: [
                { name: 'ملفات JSON', extensions: ['json'] }
            ]
        });

        if (!result.canceled) {
            this.mainWindow.webContents.send('create-backup', result.filePath);
        }
    }

    async restoreBackup() {
        const result = await dialog.showOpenDialog(this.mainWindow, {
            title: 'استعادة من نسخة احتياطية',
            filters: [
                { name: 'ملفات JSON', extensions: ['json'] },
                { name: 'جميع الملفات', extensions: ['*'] }
            ],
            properties: ['openFile']
        });

        if (!result.canceled && result.filePaths.length > 0) {
            this.mainWindow.webContents.send('restore-backup', result.filePaths[0]);
        }
    }

    showAbout() {
        const aboutWindow = new BrowserWindow({
            width: 500,
            height: 400,
            parent: this.mainWindow,
            modal: true,
            show: false,
            resizable: false,
            minimizable: false,
            maximizable: false,
            webPreferences: {
                nodeIntegration: true,
                contextIsolation: false
            }
        });

        const aboutHTML = `
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>حول التطبيق</title>
                <style>
                    body {
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        margin: 0;
                        padding: 30px;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        text-align: center;
                        direction: rtl;
                    }
                    .logo {
                        font-size: 4rem;
                        margin-bottom: 20px;
                        color: #fff;
                    }
                    h1 {
                        font-size: 1.5rem;
                        margin-bottom: 10px;
                        color: #fff;
                    }
                    .version {
                        font-size: 1.2rem;
                        margin-bottom: 20px;
                        color: #e0e0e0;
                    }
                    .description {
                        font-size: 1rem;
                        line-height: 1.6;
                        margin-bottom: 30px;
                        color: #f0f0f0;
                    }
                    .credits {
                        font-size: 0.9rem;
                        color: #d0d0d0;
                        border-top: 1px solid rgba(255,255,255,0.3);
                        padding-top: 20px;
                    }
                    button {
                        background: rgba(255,255,255,0.2);
                        border: 1px solid rgba(255,255,255,0.3);
                        color: white;
                        padding: 10px 20px;
                        border-radius: 5px;
                        cursor: pointer;
                        font-size: 1rem;
                        margin-top: 20px;
                    }
                    button:hover {
                        background: rgba(255,255,255,0.3);
                    }
                </style>
            </head>
            <body>
                <div class="logo">🎓</div>
                <h1>استمارة تقويم تقنية المعلومات</h1>
                <div class="version">الإصدار ${app.getVersion()}</div>
                <div class="description">
                    نظام شامل ومتطور لإدارة درجات الطلاب في مادة تقنية المعلومات<br>
                    يدعم جميع المراحل الدراسية من الصف الأول حتى الثاني عشر<br>
                    مع نظام طباعة احترافي وتقارير تفصيلية
                </div>
                <div class="credits">
                    تم التطوير بواسطة: فريق تطوير التطبيقات التعليمية<br>
                    تقنية: Electron + JavaScript + HTML5 + CSS3<br>
                    © 2024 جميع الحقوق محفوظة
                </div>
                <button onclick="require('electron').remote.getCurrentWindow().close()">
                    إغلاق
                </button>
            </body>
            </html>
        `;

        aboutWindow.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(aboutHTML));
        aboutWindow.once('ready-to-show', () => {
            aboutWindow.show();
        });
    }

    showUserGuide() {
        // فتح دليل المستخدم في المتصفح الافتراضي
        const guidePath = path.join(__dirname, '../README.md');
        if (fs.existsSync(guidePath)) {
            shell.openPath(guidePath);
        } else {
            this.mainWindow.webContents.send('show-user-guide');
        }
    }

    resetWindow() {
        if (this.mainWindow) {
            this.mainWindow.setSize(1400, 900);
            this.mainWindow.center();
            this.mainWindow.unmaximize();

            // حذف ملف حالة النافذة
            try {
                const userDataPath = app.getPath('userData');
                const windowStatePath = path.join(userDataPath, 'window-state.json');
                if (fs.existsSync(windowStatePath)) {
                    fs.unlinkSync(windowStatePath);
                }
            } catch (error) {
                console.log('فشل في حذف ملف حالة النافذة:', error);
            }

            showNotification('تم إعادة تعيين النافذة إلى الحالة الافتراضية', 'success');
        }
    }

    // وظائف إضافية للتطبيق
    setupAppEvents() {
        // التعامل مع أحداث النظام
        app.on('window-all-closed', () => {
            if (process.platform !== 'darwin') {
                app.quit();
            }
        });

        app.on('activate', () => {
            if (BrowserWindow.getAllWindows().length === 0) {
                this.createMainWindow();
            }
        });

        // التعامل مع الروابط الخارجية
        app.on('web-contents-created', (event, contents) => {
            contents.on('new-window', (navigationEvent, navigationURL) => {
                navigationEvent.preventDefault();
                shell.openExternal(navigationURL);
            });
        });

        // التعامل مع ملفات البروتوكول
        app.setAsDefaultProtocolClient('it-evaluation');
    }
}

// إنشاء مثيل من التطبيق
const appInstance = new ITEvaluationApp();
appInstance.setupAppEvents();
