@echo off
title IT Evaluation System Build
cls

echo.
echo ========================================
echo    Building IT Evaluation System
echo ========================================
echo.

REM Check for Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Node.js is not installed
    echo.
    echo Please install Node.js from: https://nodejs.org
    echo.
    echo Press any key to exit...
    pause
    exit /b 1
)

echo SUCCESS: Node.js found
echo.

REM Check for package.json
if not exist "package.json" (
    echo ERROR: package.json not found
    echo Make sure you are in the correct project directory
    echo.
    echo Press any key to exit...
    pause
    exit /b 1
)

echo Installing dependencies...
call npm install
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to install dependencies
    echo.
    echo Press any key to exit...
    pause
    exit /b 1
)

echo SUCCESS: Dependencies installed
echo.

echo Cleaning previous builds...
echo This may take a moment...

REM Kill any running processes that might lock files
taskkill /f /im "IT Evaluation System.exe" 2>nul
taskkill /f /im electron.exe 2>nul

REM Wait a moment for processes to close
timeout /t 2 /nobreak >nul

REM Force remove dist folder
if exist "dist" (
    echo Removing dist folder...
    rmdir /s /q "dist" 2>nul
    if exist "dist" (
        echo Forcing removal of locked files...
        rd /s /q "dist" 2>nul
        if exist "dist" (
            echo Some files are still locked. Trying alternative method...
            for /d %%i in ("dist\*") do rmdir /s /q "%%i" 2>nul
            del /f /q "dist\*.*" 2>nul
        )
    )
)

REM Remove other build folders
if exist "build\output" rmdir /s /q "build\output" 2>nul
if exist "node_modules\.cache" rmdir /s /q "node_modules\.cache" 2>nul

echo Cleanup completed.

:menu
cls
echo.
echo ========================================
echo    Choose build type
echo ========================================
echo.
echo 1. Windows (64-bit)
echo 2. Windows (32-bit)
echo 3. Windows (both)
echo 4. Portable
echo 5. All types
echo 6. Exit
echo.

set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" goto build_64
if "%choice%"=="2" goto build_32
if "%choice%"=="3" goto build_both
if "%choice%"=="4" goto build_portable
if "%choice%"=="5" goto build_all
if "%choice%"=="6" goto exit_script

echo ERROR: Invalid choice
echo Please choose a number between 1-6
echo.
pause
goto menu

:build_64
echo.
echo Building for Windows 64-bit...
call npx electron-builder --win --x64
goto check_result

:build_32
echo.
echo Building for Windows 32-bit...
call npx electron-builder --win --ia32
goto check_result

:build_both
echo.
echo Building for Windows 64-bit first...
call npx electron-builder --win --x64
if %ERRORLEVEL% NEQ 0 goto check_result

echo.
echo Cleaning for 32-bit build...
timeout /t 3 /nobreak >nul
if exist "dist\win-unpacked" rmdir /s /q "dist\win-unpacked" 2>nul

echo Building for Windows 32-bit...
call npx electron-builder --win --ia32
goto check_result

:build_portable
echo.
echo Building portable version...
call npx electron-builder --win portable
goto check_result

:build_all
echo.
echo Building all types (this will take longer)...
echo.

echo Step 1/3: Building Windows 64-bit...
call npx electron-builder --win --x64
if %ERRORLEVEL% NEQ 0 goto check_result

echo.
echo Step 2/3: Cleaning for 32-bit build...
timeout /t 3 /nobreak >nul
if exist "dist\win-unpacked" rmdir /s /q "dist\win-unpacked" 2>nul

echo Step 2/3: Building Windows 32-bit...
call npx electron-builder --win --ia32
if %ERRORLEVEL% NEQ 0 goto check_result

echo.
echo Step 3/3: Cleaning for portable build...
timeout /t 3 /nobreak >nul
if exist "dist\win-unpacked" rmdir /s /q "dist\win-unpacked" 2>nul
if exist "dist\win-ia32-unpacked" rmdir /s /q "dist\win-ia32-unpacked" 2>nul

echo Step 3/3: Building portable version...
call npx electron-builder --win portable
goto check_result

:check_result
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ERROR: Build failed
    echo Check the errors above
    echo.
    echo Press any key to return to menu...
    pause
    goto menu
)

echo.
echo SUCCESS: Build completed!
echo.

REM Show built files
if exist "dist" (
    echo Built files in dist folder:
    echo.
    dir /b "dist\*.exe" 2>nul
    dir /b "dist\*.msi" 2>nul
    dir /b "dist\*.zip" 2>nul
    dir /b "dist\*.AppImage" 2>nul
    echo.

    echo Open dist folder? (y/n)
    set /p open="Your choice: "
    if /i "%open%"=="y" (
        start "" "dist"
    )
)

echo.
echo Build completed successfully!
echo.
echo Notes:
echo - .exe file is the main installer
echo - .zip file contains portable version
echo - You can distribute any of these files
echo.

echo What would you like to do next?
echo 1. Build another version
echo 2. Exit
echo.
set /p next="Your choice (1-2): "

if "%next%"=="1" goto menu
goto exit_script

:exit_script
echo.
echo Thank you for using IT Evaluation System Builder!
echo.
pause
exit /b 0
