// إعدادات Electron المتقدمة
module.exports = {
    // إعدادات التطبيق الأساسية
    app: {
        name: 'استمارة تقويم تقنية المعلومات',
        version: '1.0.0',
        description: 'نظام شامل لإدارة درجات الطلاب في مادة تقنية المعلومات',
        author: 'IT Evaluation System Team',
        homepage: 'https://github.com/your-username/it-evaluation-system'
    },

    // إعدادات النافذة الافتراضية
    window: {
        width: 1400,
        height: 900,
        minWidth: 1000,
        minHeight: 700,
        center: true,
        resizable: true,
        maximizable: true,
        minimizable: true,
        closable: true,
        autoHideMenuBar: false,
        titleBarStyle: 'default',
        backgroundColor: '#f5f6fa',
        show: false,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true,
            webSecurity: false,
            allowRunningInsecureContent: true,
            experimentalFeatures: true
        }
    },

    // إعدادات البناء
    build: {
        appId: 'com.itevaluation.app',
        productName: 'استمارة تقويم تقنية المعلومات',
        directories: {
            output: 'dist',
            buildResources: 'build'
        },
        files: [
            '**/*',
            '!node_modules/**/*',
            '!dist/**/*',
            '!.git/**/*',
            '!.vscode/**/*',
            '!*.md',
            '!.gitignore',
            '!build.bat',
            '!build.ps1',
            '!run-app.bat'
        ],
        extraResources: [
            {
                from: 'assets/',
                to: 'assets/',
                filter: ['**/*']
            }
        ]
    },

    // إعدادات Windows
    windows: {
        target: [
            {
                target: 'nsis',
                arch: ['x64', 'ia32']
            },
            {
                target: 'portable',
                arch: ['x64', 'ia32']
            },
            {
                target: 'zip',
                arch: ['x64', 'ia32']
            }
        ],
        icon: 'assets/icon.ico',
        requestedExecutionLevel: 'asInvoker',
        artifactName: '${productName}-${version}-${arch}.${ext}',
        publisherName: 'IT Evaluation System',
        verifyUpdateCodeSignature: false
    },

    // إعدادات NSIS
    nsis: {
        oneClick: false,
        allowToChangeInstallationDirectory: true,
        allowElevation: true,
        createDesktopShortcut: true,
        createStartMenuShortcut: true,
        shortcutName: 'استمارة تقويم تقنية المعلومات',
        installerIcon: 'assets/icon.ico',
        uninstallerIcon: 'assets/icon.ico',
        installerHeaderIcon: 'assets/icon.ico',
        deleteAppDataOnUninstall: false,
        runAfterFinish: true,
        artifactName: '${productName}-Setup-${version}.${ext}',
        language: '1025', // Arabic
        warningsAsErrors: false,
        displayLanguageSelector: false,
        license: 'LICENSE'
    },

    // إعدادات macOS
    mac: {
        target: [
            {
                target: 'dmg',
                arch: ['x64', 'arm64']
            }
        ],
        icon: 'assets/icon.icns',
        category: 'public.app-category.education',
        artifactName: '${productName}-${version}-${arch}.${ext}'
    },

    // إعدادات Linux
    linux: {
        target: [
            {
                target: 'AppImage',
                arch: ['x64']
            },
            {
                target: 'deb',
                arch: ['x64']
            }
        ],
        icon: 'assets/icon.png',
        category: 'Education',
        artifactName: '${productName}-${version}-${arch}.${ext}'
    },

    // إعدادات التحديث التلقائي
    autoUpdater: {
        enabled: true,
        checkForUpdatesOnStart: true,
        autoDownload: true,
        autoInstallOnAppQuit: true,
        provider: 'github',
        owner: 'your-username',
        repo: 'it-evaluation-system'
    },

    // إعدادات الأمان
    security: {
        allowRunningInsecureContent: false,
        experimentalFeatures: false,
        webSecurity: true,
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: './electron/preload.js'
    },

    // إعدادات التطوير
    development: {
        openDevTools: true,
        reloadOnChange: true,
        showConsole: true,
        enableLogging: true
    },

    // إعدادات الإنتاج
    production: {
        openDevTools: false,
        reloadOnChange: false,
        showConsole: false,
        enableLogging: false,
        minifyCode: true,
        optimizeImages: true
    },

    // إعدادات قاعدة البيانات
    database: {
        name: 'it_evaluation.db',
        location: 'userData', // userData, temp, desktop, documents
        backup: {
            enabled: true,
            interval: 24 * 60 * 60 * 1000, // 24 ساعة
            maxBackups: 7
        }
    },

    // إعدادات الطباعة
    printing: {
        defaultPrinter: null,
        paperSize: 'A4',
        orientation: 'portrait',
        margins: {
            top: 20,
            bottom: 20,
            left: 20,
            right: 20
        },
        quality: 'high',
        colorMode: 'color'
    },

    // إعدادات التصدير
    export: {
        defaultFormat: 'xlsx',
        defaultLocation: 'downloads',
        includeImages: true,
        includeCharts: true,
        compression: 'medium'
    },

    // إعدادات اللغة
    localization: {
        defaultLanguage: 'ar',
        supportedLanguages: ['ar', 'en'],
        rtlSupport: true,
        dateFormat: 'DD/MM/YYYY',
        timeFormat: '24h',
        numberFormat: 'arabic'
    },

    // إعدادات الأداء
    performance: {
        enableHardwareAcceleration: true,
        enableGPUAcceleration: true,
        maxMemoryUsage: '2GB',
        enableCaching: true,
        preloadImages: true
    },

    // إعدادات الشبكة
    network: {
        timeout: 30000, // 30 ثانية
        retries: 3,
        proxy: null,
        userAgent: 'IT Evaluation System/1.0.0'
    },

    // إعدادات السجلات
    logging: {
        level: 'info', // error, warn, info, debug
        file: 'app.log',
        maxSize: '10MB',
        maxFiles: 5,
        console: true
    }
};

// تصدير الإعدادات
if (typeof module !== 'undefined' && module.exports) {
    module.exports = module.exports;
}
