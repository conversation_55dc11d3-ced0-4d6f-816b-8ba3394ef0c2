{"name": "it-evaluation-system", "version": "1.0.0", "description": "IT Evaluation System", "main": "electron/main.js", "homepage": "./", "author": {"name": "IT Evaluation System", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "serve": "http-server . -p 8080 -o"}, "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4", "http-server": "^14.1.1"}, "dependencies": {"electron-updater": "^6.1.4"}, "build": {"appId": "com.itevaluation.app", "productName": "IT Evaluation System", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules/**/*", "!dist/**/*", "!.git/**/*", "!*.md"], "win": {"target": "nsis"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}