@echo off
chcp 65001 >nul
title استمارة تقويم تقنية المعلومات

echo.
echo ========================================
echo    استمارة تقويم تقنية المعلومات
echo ========================================
echo.

REM التحقق من وجود Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Node.js غير مثبت على النظام
    echo.
    echo يرجى تثبيت Node.js من الرابط التالي:
    echo https://nodejs.org
    echo.
    echo أو يمكنك فتح ملف index.html مباشرة في المتصفح
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على Node.js
echo.

REM التحقق من وجود package.json
if not exist "package.json" (
    echo ❌ ملف package.json غير موجود
    echo تأكد من أنك في المجلد الصحيح للمشروع
    pause
    exit /b 1
)

REM التحقق من وجود node_modules
if not exist "node_modules" (
    echo 📦 تثبيت التبعيات...
    call npm install
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ فشل في تثبيت التبعيات
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت التبعيات بنجاح
    echo.
)

echo.
echo اختر طريقة التشغيل:
echo 1. تشغيل كتطبيق سطح مكتب (Electron)
echo 2. تشغيل في المتصفح (Web)
echo 3. بناء تطبيق exe
echo.

set /p mode="أدخل اختيارك (1-3): "

if "%mode%"=="1" (
    echo 🚀 بدء تشغيل تطبيق سطح المكتب...
    echo.
    echo سيتم فتح التطبيق في نافذة منفصلة...
    echo لإغلاق التطبيق، أغلق النافذة أو اضغط Ctrl+C
    echo.
    call npm start
) else if "%mode%"=="2" (
    echo 🌐 بدء تشغيل الخادم المحلي...
    echo.
    echo سيتم فتح التطبيق في المتصفح...
    echo الرابط: http://localhost:8080
    echo لإغلاق الخادم، اضغط Ctrl+C
    echo.
    call npm run serve
) else if "%mode%"=="3" (
    echo 🔨 بدء بناء تطبيق exe...
    echo.
    call build.bat
) else (
    echo ❌ اختيار غير صحيح
    pause
    exit /b 1
)

echo.
echo تم إغلاق التطبيق
pause
