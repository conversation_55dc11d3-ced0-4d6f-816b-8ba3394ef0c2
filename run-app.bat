@echo off
chcp 65001 >nul
title تشغيل استمارة تقويم تقنية المعلومات

echo.
echo ========================================
echo    استمارة تقويم تقنية المعلومات
echo ========================================
echo.

REM البحث عن التطبيق المبني
set "APP_FOUND=0"
set "APP_PATH="

REM البحث في مجلد dist
if exist "dist\*.exe" (
    for %%f in (dist\*.exe) do (
        if not "%%~nf"=="Uninstall" (
            set "APP_PATH=%%f"
            set "APP_FOUND=1"
            goto :found
        )
    )
)

REM البحث في مجلد win-unpacked
if exist "dist\win-unpacked\*.exe" (
    for %%f in (dist\win-unpacked\*.exe) do (
        set "APP_PATH=%%f"
        set "APP_FOUND=1"
        goto :found
    )
)

:found
if "%APP_FOUND%"=="1" (
    echo ✅ تم العثور على التطبيق: %APP_PATH%
    echo.
    echo 🚀 بدء تشغيل التطبيق...
    echo.
    
    start "" "%APP_PATH%"
    
    echo ✅ تم تشغيل التطبيق بنجاح
    echo.
    echo ملاحظة: إذا لم يظهر التطبيق، تحقق من شريط المهام
    
) else (
    echo ❌ لم يتم العثور على التطبيق المبني
    echo.
    echo يرجى بناء التطبيق أولاً باستخدام:
    echo   build.bat
    echo أو
    echo   npm run build-win
    echo.
    echo أو يمكنك تشغيل التطبيق في وضع التطوير:
    echo   start.bat
)

echo.
pause
